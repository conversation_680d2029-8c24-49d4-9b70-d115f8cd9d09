/* empty css                  *//* empty css                *//* empty css                 */import{s as e}from"./index-c4e5aa7e.js";import{r as a,aP as l,d as s,e as t,f as u,g as i,q as r,j as n,m as o,k as m,F as v,z as p,T as d,h as c,aQ as f,i as x,a5 as y,C as j,E as h,H as g}from"./index-45da9c2f.js";import{s as k}from"./index-8d2dbf76.js";/* empty css                   */const w={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},z=["innerHTML"],b={class:"flex justify-center mt-10"},C={__name:"index",setup(C){const E=a(""),H=a([]),S=a([]),T=a(5),V=()=>{if(a=E.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!E.value)return y.warning("请输入英文内容");var a;e({text:E.value}).then((e=>{e&&e&&e.data&&(H.value=e.data,H.value=k(H.value),T.value=5,A())}))},A=()=>{let e=[];5==T.value?(e=[0,5],T.value=3):3==T.value?(e=[5,8],T.value=2):2==T.value&&(e=[8,10],T.value=5),S.value=JSON.parse(JSON.stringify(H.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,k=h,C=g,H=l("copy");return s(),t("div",w,[u(y,{modelValue:i(E),"onUpdate:modelValue":a[0]||(a[0]=e=>r(E)?E.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(k,{type:"primary",onClick:V},{default:o((()=>[m("改 写")])),_:1})]),i(S)&&i(S).length?(s(),t("div",_,[(s(!0),t(v,null,p(i(S),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,z),d((s(),c(C,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(i(f))])),_:2},1024)),[[H,e.text]])])))),128)),n("div",b,[u(k,{type:"primary",link:"",onClick:A},{default:o((()=>[m("换一换")])),_:1})])])):x("",!0)])}}};export{C as default};
