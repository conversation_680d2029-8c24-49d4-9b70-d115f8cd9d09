import{M as a,a as e,o as s,a3 as n,al as i,d as o,e as t}from"https://static.medsci.cn/ai-write/static/index-f1efeeee.js";const r=a({__name:"index",setup(a){const r=e(),c=JSON.parse(decodeURIComponent(r.params.payInfo));return s((async()=>{const a=navigator.userAgent;if(null!=a)if(a.includes("MicroMessenger"))n.warning("请打开支付宝扫码");else if(a.includes("AlipayClient")){const a=await i(c);location.href=a}})),(a,e)=>(o(),t("div"))}});export{r as default};
