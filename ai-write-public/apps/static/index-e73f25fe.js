/* empty css                  *//* empty css                   */import{r as e,x as a,u as t,w as l,c as o,d as i,i as n,t as r,f as s,g as u,l as d,F as c,y as v,e as p,z as m,j as f,h,A as g,B as y,C as b,D as w,G as x,E as k,H as I,I as _,J as S,K as C,L as T,M as $,N as B,O as j,n as z,o as q,P as N,Q as O,R,S as A,q as M,T as E,U as V,V as U,W as L,a as H,X as W,Y as D,Z as J,$ as P,a0 as F,b as X,a1 as Z,a2 as Y,a3 as G,a4 as K,a5 as Q,a6 as ee}from"./index-db483efc.js";import{g as ae}from"./index-1ad4af37.js";/* empty css                  *//* empty css                  *//* empty css                 */import{_ as te}from"./_plugin-vue_export-helper-1b428a4d.js";import{c as le,r as oe,g as ie,s as ne,i as re,o as se,a as ue,n as de,m as ce,b as ve,u as pe,d as me,e as fe,f as he,h as ge,j as ye,k as be,w as we,l as xe,p as ke,t as Ie,q as _e,v as Se,x as Ce,y as Te,z as $e,A as Be,B as je,C as ze,D as qe,E as Ne,F as Oe,G as Re,H as Ae,I as Me,J as Ee,K as Ve,L as Ue,M as Le,N as He}from"./use-touch-5f578dde.js";import{r as We,a as De,f as Je}from"./use-route-c7cac0d1.js";const Pe={class:"p-3 flex-1 rounded-md"},Fe={class:"text-[14px] font-bold mb-2 text-gray-600"},Xe={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(_,{expose:S,emit:C}){const T=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),$=t(),B=e([]),j=e({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),z=e(""),q=_,N=q.type,O=q.fileVerify,R=q.label,A=q.required,M=q.max_length,E=q.options;"file"==N&&(z.value=null),"file-list"==N&&(z.value=[]);const V={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},U=()=>{let e="";return O.forEach(((a,t)=>{t<O.length-1?e+=V[a].join(",")+",":e+=V[a].join(",")})),e},L=C,H=(e,a,t)=>{},W=()=>{z.value=""},D=async e=>{const{file:a,onSuccess:t,onError:l}=e,o=new FormData;o.append("file",a),o.append("appId",$.params.uuid),o.append("user",T.value.userName);try{const e=await g(o);"file-list"==N?z.value.push({type:j.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):z.value={type:j.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},t(e,a)}catch(i){l(i)}return!1};E&&E.length>0&&(z.value=E[0]);return S({updateMessage:()=>{E&&E.length>0?z.value=E[0]:"file"==N?(z.value=null,B.value=[]):"file-list"==N?(z.value=[],B.value=[]):z.value=""}}),l(z,(e=>{L("update:value",e)}),{immediate:!0,deep:!0}),(e,a)=>{const t=y,l=b,g=w,_=x,S=k,C=I;return o(),i("div",Pe,[n("div",Fe,r(s(R)),1),"paragraph"===s(N)||"text-input"===s(N)?(o(),u(t,{key:0,modelValue:z.value,"onUpdate:modelValue":a[0]||(a[0]=e=>z.value=e),type:"paragraph"===s(N)?"textarea":"text",rows:5,required:s(A),placeholder:`${s(R)}`,"show-word-limit":"",resize:"none",maxlength:s(M)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===s(N)?(o(),u(t,{key:1,modelValue:z.value,"onUpdate:modelValue":a[1]||(a[1]=e=>z.value=e),modelModifiers:{number:!0},type:"number",required:s(A),placeholder:`${s(R)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===s(N)?(o(),u(g,{key:2,modelValue:z.value,"onUpdate:modelValue":a[2]||(a[2]=e=>z.value=e),required:s(A),placeholder:`${s(R)}`},{default:d((()=>[(o(!0),i(c,null,v(s(E),(e=>(o(),u(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===s(N)||"file-list"===s(N)?(o(),u(C,{key:3,"file-list":B.value,"onUpdate:fileList":a[3]||(a[3]=e=>B.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":W,"before-remove":e.beforeRemove,limit:s(M),accept:U(),"auto-upload":!0,"on-Success":H,"http-request":D,"on-exceed":e.handleExceed},{default:d((()=>[p(S,{disabled:B.value.length==s(M)},{default:d((()=>[p(_,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:d((()=>[p(s(m))])),_:1}),a[4]||(a[4]=f("从本地上传"))])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):h("",!0)])}}};let Ze=0;function Ye(){const e=_(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++Ze}`}function Ge(e,a){if(!re||!window.IntersectionObserver)return;const t=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&t.unobserve(e.value)};C(l),T(l),se((()=>{e.value&&t.observe(e.value)}))}const[Ke,Qe]=ue("sticky");const ea=ke($({name:Ke,props:{zIndex:de,position:ce("top"),container:Object,offsetTop:ve(0),offsetBottom:ve(0)},emits:["scroll","change"],setup(a,{emit:t,slots:o}){const i=e(),n=pe(i),r=B({fixed:!1,width:0,height:0,transform:0}),s=e(!1),u=j((()=>me("top"===a.position?a.offsetTop:a.offsetBottom))),d=j((()=>{if(s.value)return;const{fixed:e,height:a,width:t}=r;return e?{width:`${t}px`,height:`${a}px`}:void 0})),c=j((()=>{if(!r.fixed||s.value)return;const e=fe(he(a.zIndex),{width:`${r.width}px`,height:`${r.height}px`,[a.position]:`${u.value}px`});return r.transform&&(e.transform=`translate3d(0, ${r.transform}px, 0)`),e})),v=()=>{if(!i.value||ye(i))return;const{container:e,position:l}=a,o=be(i),n=ie(window);if(r.width=o.width,r.height=o.height,"top"===l)if(e){const a=be(e),t=a.bottom-u.value-r.height;r.fixed=u.value>o.top&&a.bottom>0,r.transform=t<0?t:0}else r.fixed=u.value>o.top;else{const{clientHeight:a}=document.documentElement;if(e){const t=be(e),l=a-t.top-u.value-r.height;r.fixed=a-u.value<o.bottom&&a>t.top,r.transform=l<0?-l:0}else r.fixed=a-u.value<o.bottom}(e=>{t("scroll",{scrollTop:e,isFixed:r.fixed})})(n)};return l((()=>r.fixed),(e=>t("change",e))),ge("scroll",v,{target:n,passive:!0}),Ge(i,v),l([we,xe],(()=>{i.value&&!ye(i)&&r.fixed&&(s.value=!0,z((()=>{const e=be(i);r.width=e.width,r.height=e.height,s.value=!1})))})),()=>{var e;return p("div",{ref:i,style:d.value},[p("div",{class:Qe({fixed:r.fixed&&!s.value}),style:c.value},[null==(e=o.default)?void 0:e.call(o)])])}}})),[aa,ta]=ue("swipe"),la={loop:Ie,width:de,height:de,vertical:Boolean,autoplay:ve(0),duration:ve(500),touchable:Ie,lazyRender:Boolean,initialSwipe:ve(0),indicatorColor:String,showIndicators:Ie,stopPropagation:Ie},oa=Symbol(aa);const ia=ke($({name:aa,props:la,emits:["change","dragStart","dragEnd"],setup(a,{emit:t,slots:o}){const i=e(),n=e(),r=B({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let s=!1;const u=_e(),{children:d,linkChildren:c}=Se(oa),v=j((()=>d.length)),m=j((()=>r[a.vertical?"height":"width"])),f=j((()=>a.vertical?u.deltaY.value:u.deltaX.value)),h=j((()=>{if(r.rect){return(a.vertical?r.rect.height:r.rect.width)-m.value*v.value}return 0})),g=j((()=>m.value?Math.ceil(Math.abs(h.value)/m.value):v.value)),y=j((()=>v.value*m.value)),b=j((()=>(r.active+v.value)%v.value)),w=j((()=>{const e=a.vertical?"vertical":"horizontal";return u.direction.value===e})),x=j((()=>{const e={transitionDuration:`${r.swiping?0:a.duration}ms`,transform:`translate${a.vertical?"Y":"X"}(${+r.offset.toFixed(2)}px)`};if(m.value){const t=a.vertical?"height":"width",l=a.vertical?"width":"height";e[t]=`${y.value}px`,e[l]=a[l]?`${a[l]}px`:""}return e})),k=(e,t=0)=>{let l=e*m.value;a.loop||(l=Math.min(l,-h.value));let o=t-l;return a.loop||(o=ze(o,h.value,0)),o},I=({pace:e=0,offset:l=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=r,n=(e=>{const{active:t}=r;return e?a.loop?ze(t+e,-1,v.value):ze(t+e,0,g.value):t})(e),s=k(n,l);if(a.loop){if(d[0]&&s!==h.value){const e=s<h.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==s){const e=s>0;d[v.value-1].setOffset(e?-y.value:0)}}r.active=n,r.offset=s,o&&n!==i&&t("change",b.value)},_=()=>{r.swiping=!0,r.active<=-1?I({pace:v.value}):r.active>=v.value&&I({pace:-v.value})},S=()=>{_(),u.reset(),Be((()=>{r.swiping=!1,I({pace:1,emitChange:!0})}))};let $;const O=()=>clearTimeout($),R=()=>{O(),+a.autoplay>0&&v.value>1&&($=setTimeout((()=>{S(),R()}),+a.autoplay))},A=(e=+a.initialSwipe)=>{if(!i.value)return;const t=()=>{var t,l;if(!ye(i)){const e={width:i.value.offsetWidth,height:i.value.offsetHeight};r.rect=e,r.width=+(null!=(t=a.width)?t:e.width),r.height=+(null!=(l=a.height)?l:e.height)}v.value&&-1===(e=Math.min(v.value-1,e))&&(e=v.value-1),r.active=e,r.swiping=!0,r.offset=k(e),d.forEach((e=>{e.setOffset(0)})),R()};ye(i)?z().then(t):t()},M=()=>A(r.active);let E;const V=e=>{!a.touchable||e.touches.length>1||(u.start(e),s=!1,E=Date.now(),O(),_())},U=()=>{if(!a.touchable||!r.swiping)return;const e=Date.now()-E,l=f.value/e;if((Math.abs(l)>.25||Math.abs(f.value)>m.value/2)&&w.value){const e=a.vertical?u.offsetY.value:u.offsetX.value;let t=0;t=a.loop?e>0?f.value>0?-1:1:0:-Math[f.value>0?"ceil":"floor"](f.value/m.value),I({pace:t,emitChange:!0})}else f.value&&I({pace:0});s=!1,r.swiping=!1,t("dragEnd",{index:b.value}),R()},L=(e,t)=>{const l=t===b.value,o=l?{backgroundColor:a.indicatorColor}:void 0;return p("i",{style:o,class:ta("indicator",{active:l})},null)};return Ce({prev:()=>{_(),u.reset(),Be((()=>{r.swiping=!1,I({pace:-1,emitChange:!0})}))},next:S,state:r,resize:M,swipeTo:(e,t={})=>{_(),u.reset(),Be((()=>{let l;l=a.loop&&e===v.value?0===r.active?0:e:e%v.value,t.immediate?Be((()=>{r.swiping=!1})):r.swiping=!1,I({pace:l-r.active,emitChange:!0})}))}}),c({size:m,props:a,count:v,activeIndicator:b}),l((()=>a.initialSwipe),(e=>A(+e))),l(v,(()=>A(r.active))),l((()=>a.autoplay),R),l([we,xe,()=>a.width,()=>a.height],M),l(Te(),(e=>{"visible"===e?R():O()})),q(A),N((()=>A(r.active))),$e((()=>A(r.active))),C(O),T(O),ge("touchmove",(e=>{if(a.touchable&&r.swiping&&(u.move(e),w.value)){!a.loop&&(0===r.active&&f.value>0||r.active===v.value-1&&f.value<0)||(je(e,a.stopPropagation),I({offset:f.value}),s||(t("dragStart",{index:b.value}),s=!0))}}),{target:n}),()=>{var e;return p("div",{ref:i,class:ta()},[p("div",{ref:n,style:x.value,class:ta("track",{vertical:a.vertical}),onTouchstartPassive:V,onTouchend:U,onTouchcancel:U},[null==(e=o.default)?void 0:e.call(o)]),o.indicator?o.indicator({active:b.value,total:v.value}):a.showIndicators&&v.value>1?p("div",{class:ta("indicators",{vertical:a.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[na,ra]=ue("tabs");var sa=$({name:na,props:{count:qe(Number),inited:Boolean,animated:Boolean,duration:qe(de),swipeable:Boolean,lazyRender:Boolean,currentIndex:qe(Number)},emits:["change"],setup(a,{emit:t,slots:o}){const i=e(),n=e=>t("change",e),r=()=>{var e;const t=null==(e=o.default)?void 0:e.call(o);return a.animated||a.swipeable?p(ia,{ref:i,loop:!1,class:ra("track"),duration:1e3*+a.duration,touchable:a.swipeable,lazyRender:a.lazyRender,showIndicators:!1,onChange:n},{default:()=>[t]}):t},s=e=>{const t=i.value;t&&t.state.active!==e&&t.swipeTo(e,{immediate:!a.inited})};return l((()=>a.currentIndex),s),q((()=>{s(a.currentIndex)})),Ce({swipeRef:i}),()=>p("div",{class:ra("content",{animated:a.animated||a.swipeable})},[r()])}});const[ua,da]=ue("tabs"),ca={type:ce("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:ve(0),duration:ve(.3),animated:Boolean,ellipsis:Ie,swipeable:Boolean,scrollspy:Boolean,offsetTop:ve(0),background:String,lazyRender:Ie,showHeader:Ie,lineWidth:de,lineHeight:de,beforeChange:Function,swipeThreshold:ve(5),titleActiveColor:String,titleInactiveColor:String},va=Symbol(ua);var pa=$({name:ua,props:ca,emits:["change","scroll","rendered","clickTab","update:active"],setup(a,{emit:t,slots:o}){let i,n,r,s,u;const d=e(),c=e(),v=e(),m=e(),f=Ye(),h=pe(d),[g,y]=function(){const a=e([]),t=[];return S((()=>{a.value=[]})),[a,e=>(t[e]||(t[e]=t=>{a.value[e]=t}),t[e])]}(),{children:b,linkChildren:w}=Se(va),x=B({inited:!1,position:"",lineStyle:{},currentIndex:-1}),k=j((()=>b.length>+a.swipeThreshold||!a.ellipsis||a.shrink)),I=j((()=>({borderColor:a.color,background:a.background}))),_=(e,a)=>{var t;return null!=(t=e.name)?t:a},C=j((()=>{const e=b[x.currentIndex];if(e)return _(e,x.currentIndex)})),T=j((()=>me(a.offsetTop))),$=j((()=>a.sticky?T.value+i:0)),q=e=>{const t=c.value,l=g.value;if(!(k.value&&t&&l&&l[x.currentIndex]))return;const o=l[x.currentIndex].$el,i=o.offsetLeft-(t.offsetWidth-o.offsetWidth)/2;s&&s(),s=function(e,a,t){let l,o=0;const i=e.scrollLeft,n=0===t?1:Math.round(1e3*t/16);let r=i;return function t(){r+=(a-i)/n,e.scrollLeft=r,++o<n&&(l=oe(t))}(),function(){le(l)}}(t,i,e?0:+a.duration)},O=()=>{const e=x.inited;z((()=>{const t=g.value;if(!t||!t[x.currentIndex]||"line"!==a.type||ye(d.value))return;const l=t[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=a,n=l.offsetLeft+l.offsetWidth/2,r={width:Ne(o),backgroundColor:a.color,transform:`translateX(${n}px) translateX(-50%)`};if(e&&(r.transitionDuration=`${a.duration}s`),Oe(i)){const e=Ne(i);r.height=e,r.borderRadius=e}x.lineStyle=r}))},R=(e,l)=>{const o=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(e);if(!Oe(o))return;const i=b[o],n=_(i,o),s=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,l||q(),O()),n!==a.active&&(t("update:active",n),s&&t("change",n,i.title)),r&&!a.scrollspy&&Ae(Math.ceil(Me(d.value)-T.value))},A=(e,a)=>{const t=b.find(((a,t)=>_(a,t)===e)),l=t?b.indexOf(t):0;R(l,a)},M=(e=!1)=>{if(a.scrollspy){const t=b[x.currentIndex].$el;if(t&&h.value){const l=Me(t,h.value)-$.value;n=!0,u&&u(),u=function(e,a,t,l){let o,i=ie(e);const n=i<a,r=0===t?1:Math.round(1e3*t/16),s=(a-i)/r;return function t(){i+=s,(n&&i>a||!n&&i<a)&&(i=a),ne(e,i),n&&i<a||!n&&i>a?o=oe(t):l&&(o=oe(l))}(),function(){le(o)}}(h.value,l,e?0:+a.duration,(()=>{n=!1}))}}},E=(e,l,o)=>{const{title:i,disabled:n}=b[l],r=_(b[l],l);n||(Ee(a.beforeChange,{args:[r],done:()=>{R(l),M()}}),We(e)),t("clickTab",{name:r,title:i,event:o,disabled:n})},V=e=>{r=e.isFixed,t("scroll",e)},U=()=>{if("line"===a.type&&b.length)return p("div",{class:da("line"),style:x.lineStyle},null)},L=()=>{var e,t,l;const{type:i,border:n,sticky:r}=a,s=[p("div",{ref:r?void 0:v,class:[da("wrap"),{[Re]:"line"===i&&n}]},[p("div",{ref:c,role:"tablist",class:da("nav",[i,{shrink:a.shrink,complete:k.value}]),style:I.value,"aria-orientation":"horizontal"},[null==(e=o["nav-left"])?void 0:e.call(o),b.map((e=>e.renderTitle(E))),U(),null==(t=o["nav-right"])?void 0:t.call(o)])]),null==(l=o["nav-bottom"])?void 0:l.call(o)];return r?p("div",{ref:v},[s]):s},H=()=>{O(),z((()=>{var e,a;q(!0),null==(a=null==(e=m.value)?void 0:e.swipeRef.value)||a.resize()}))};l((()=>[a.color,a.duration,a.lineWidth,a.lineHeight]),O),l(we,H),l((()=>a.active),(e=>{e!==C.value&&A(e)})),l((()=>b.length),(()=>{x.inited&&(A(a.active),O(),z((()=>{q(!0)})))}));return Ce({resize:H,scrollTo:e=>{z((()=>{A(e),M(!0)}))}}),N(O),$e(O),se((()=>{A(a.active,!0),z((()=>{x.inited=!0,v.value&&(i=be(v.value).height),q(!0)}))})),Ge(d,O),ge("scroll",(()=>{if(a.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=be(b[e].$el);if(a>$.value)return 0===e?0:e-1}return b.length-1})();R(e)}}),{target:h,passive:!0}),w({id:f,props:a,setLine:O,scrollable:k,onRendered:(e,a)=>t("rendered",e,a),currentName:C,setTitleRefs:y,scrollIntoView:q}),()=>p("div",{ref:d,class:da([a.type])},[a.showHeader?a.sticky?p(ea,{container:d.value,offsetTop:T.value,onScroll:V},{default:()=>[L()]}):L():null,p(sa,{ref:m,count:b.length,inited:x.inited,animated:a.animated,duration:a.duration,swipeable:a.swipeable,lazyRender:a.lazyRender,currentIndex:x.currentIndex,onChange:R},{default:()=>{var e;return[null==(e=o.default)?void 0:e.call(o)]}})])}});const ma=Symbol(),[fa,ha]=ue("tab"),ga=$({name:fa,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:de,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:Ie},setup(e,{slots:a}){const t=j((()=>{const a={},{type:t,color:l,disabled:o,isActive:i,activeColor:n,inactiveColor:r}=e;l&&"card"===t&&(a.borderColor=l,o||(i?a.backgroundColor=l:a.color=l));const s=i?n:r;return s&&(a.color=s),a})),l=()=>{const t=p("span",{class:ha("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||Oe(e.badge)&&""!==e.badge?p(Ve,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[t]}):t};return()=>p("div",{id:e.id,role:"tab",class:[ha([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:t.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[ya,ba]=ue("swipe-item");const wa=ke($({name:ya,setup(e,{slots:a}){let t;const l=B({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=Ue(oa);if(!o)return;const n=j((()=>{const e={},{vertical:a}=o.props;return o.size.value&&(e[a?"height":"width"]=`${o.size.value}px`),l.offset&&(e.transform=`translate${a?"Y":"X"}(${l.offset}px)`),e})),r=j((()=>{const{loop:e,lazyRender:a}=o.props;if(!a||t)return!0;if(!l.mounted)return!1;const n=o.activeIndicator.value,r=o.count.value-1,s=0===n&&e?r:n-1,u=n===r&&e?0:n+1;return t=i.value===n||i.value===s||i.value===u,t}));return q((()=>{z((()=>{l.mounted=!0}))})),Ce({setOffset:e=>{l.offset=e}}),()=>{var e;return p("div",{class:ba(),style:n.value},[r.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[xa,ka]=ue("tab");const Ia=ke($({name:xa,props:fe({},De,{dot:Boolean,name:de,badge:de,title:String,disabled:Boolean,titleClass:Le,titleStyle:[String,Object],showZeroBadge:Ie}),setup(a,{slots:t}){const o=Ye(),i=e(!1),n=_(),{parent:r,index:s}=Ue(va);if(!r)return;const u=()=>{var e;return null!=(e=a.name)?e:s.value},d=j((()=>{const e=u()===r.currentName.value;return e&&!i.value&&(i.value=!0,r.props.lazyRender&&z((()=>{r.onRendered(u(),a.title)}))),e})),c=e(""),v=e("");O((()=>{const{titleClass:e,titleStyle:t}=a;c.value=e?R(e):"",v.value=t&&"string"!=typeof t?A(M(t)):t}));const m=e(!d.value);return l(d,(e=>{e?m.value=!1:Be((()=>{m.value=!0}))})),l((()=>a.title),(()=>{r.setLine(),r.scrollIntoView()})),E(ma,d),Ce({id:o,renderTitle:e=>p(ga,L({key:o,id:`${r.id}-${s.value}`,ref:r.setTitleRefs(s.value),style:v.value,class:c.value,isActive:d.value,controls:o,scrollable:r.scrollable.value,activeColor:r.props.titleActiveColor,inactiveColor:r.props.titleInactiveColor,onClick:a=>e(n.proxy,s.value,a)},He(r.props,["type","color","shrink"]),He(a,["dot","badge","title","disabled","showZeroBadge"])),{title:t.title})}),()=>{var e;const a=`${r.id}-${s.value}`,{animated:l,swipeable:n,scrollspy:u,lazyRender:c}=r.props;if(!t.default&&!l)return;const v=u||d.value;if(l||n)return p(wa,{id:o,role:"tabpanel",class:ka("panel-wrapper",{inactive:m.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":a,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[p("div",{class:ka("panel")},[null==(e=t.default)?void 0:e.call(t)])]}});const f=i.value||u||!c?null==(e=t.default)?void 0:e.call(t):null;return V(p("div",{id:o,role:"tabpanel",class:ka("panel"),tabindex:v?0:-1,"aria-labelledby":a,"data-allow-mismatch":"attribute"},[f]),[[U,v]])}}})),_a=ke(pa),Sa={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Ca={class:"pc_container",style:{display:"flex"}},Ta={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},$a={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Ba={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},ja=["src"],za=["src"],qa={class:"mobile_container"},Na={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Oa={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},Ra=te({__name:"index",setup(m){const g=ae("loading.png"),y=ae("copy.png"),b=B({}),w=t(),x={},I=e([]),_=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),S=e(null),{locale:C}=H(),T=W(),$=e(!1);let z=e("a"),N=e("");const O=e(""),A=e(null),M=e(null);let E;const U=async()=>{var e;await Z({appId:w.params.uuid,user:_.value.userName,mode:null==(e=S.value)?void 0:e.mode,task_id:O.value}),setTimeout((()=>{pe.abort(),me=!0,ce.value=[],ye()}),0)};q((async()=>{var e,t;const l=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${l}px`);if(["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=w.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=w.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),C.value=D(),a.get("userInfo"))_e(),ue();else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=J("current_location_country",1);e&&"中国"!=e?T.push("/login"):window.addLoginDom()}})),l(N,(()=>{N.value&&(z.value="b")}));const L=()=>{w.params.uuid&&Y({appId:w.params.uuid,user:_.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(I.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],t=e[a].variable;x[t]={label:e[a].label},b[t]=""})))}))},te=j((()=>!!I.value.length)),le=j((()=>I.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),oe=()=>{w.params.uuid&&G({appId:w.params.uuid,user:_.value.userName}).then((e=>{S.value={...e}}))},ie=e(!1),ne=e(!1),re=e(!1),se=e(!1),ue=async()=>{var e,a;let t=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=_.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:w.params.uuid,userUuid:null==(a=_.value)?void 0:a.openid}];await P.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",t)},de=()=>{var e,a;if(0!=le.value.length||(t=b,Object.values(t).some((e=>e)))){var t;for(let e in b)if(le.value.includes(e)&&!b[e])return void K({message:`${x[e].label}为必填项！`,type:"error"});(null==(e=S.value)?void 0:e.mode)&&(["advanced-chat","chat"].includes(null==(a=S.value)?void 0:a.mode)?K({type:"success",message:"计划中，敬请期待..."}):"completion"==S.value.mode?ge():he())}else K({message:"请输入您的问题。",type:"error"})},ce=e([]),ve=e(!1);let pe,me=!1,fe=!1;const he=async()=>{N.value="",ce.value=[],ve.value=!1,me=!1,pe=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run`;0,ie.value=!0,ne.value=!0,se.value=!1,await Je(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:_.value.userName,inputs:{...b,outputLanguage:b.outputLanguage?b.outputLanguage:"中文"==Q()?"简体中文":Q()},files:[],response_mode:"streaming",appUuid:w.params.appUuid}),onmessage(e){var a,t;if(e.data.trim())try{const l=JSON.parse(e.data);if(O.value=l.task_id,l.error)throw new Error(l.error);"text_chunk"===l.event&&($.value=!0,ce.value.push(null==(a=null==l?void 0:l.data)?void 0:a.text),ve.value||ye()),"workflow_started"===l.event&&(ie.value=!1,re.value=!0),"workflow_finished"===l.event&&($.value||(ce.value.push(null==(t=null==l?void 0:l.data)?void 0:t.outputs.text),ve.value||ye()),me=!0,z.value="b")}catch(l){xe(l)}},onerror(e){xe(e)},signal:pe.signal,openWhenHidden:!0})}catch(e){xe()}},ge=async()=>{N.value="",ce.value=[],ve.value=!1,me=!1,pe=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages`;0,ie.value=!0,ne.value=!0,se.value=!1,await Je(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:_.value.userName,inputs:{...b,outputLanguage:Q()},files:[],response_mode:"streaming",appUuid:w.params.appUuid}),onmessage(e){if(ie.value=!1,re.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(O.value=a.task_id,a.error)throw new Error(a.error);"message"===a.event&&(ce.value.push(null==a?void 0:a.answer),ve.value||ye()),"message_end"===a.event&&(z.value="b",me=!0)}catch(a){xe(a)}},onerror(e){xe(e)},signal:pe.signal,openWhenHidden:!0})}catch(e){xe()}},ye=()=>{if(0===ce.value.length)return ve.value=!1,fe=!0,void be();ve.value=!0;const e=ce.value.shift();we(e).then((()=>{ye()}))},be=()=>{fe&&me&&(ne.value=!1,re.value=!1,se.value=!0)},we=e=>new Promise((a=>{let t=0;E=setInterval((()=>{if(t<e.length){N.value+=e[t++];const a=document.getElementById("typing-area");a.scrollTop=a.scrollHeight}else clearInterval(E),a()}),15)})),xe=()=>{setTimeout((()=>{pe.abort()}),0),ie.value=!1,re.value=!1,ne.value=!1,ve.value=!1,K.error("访问太火爆了！休息下，请稍后再试！"),N.value="访问太火爆了！休息下，请稍后再试！"},ke=async()=>{try{await navigator.clipboard.writeText(N.value),K({type:"success",message:"复制成功"})}catch(e){K(e)}},Ie=()=>{for(let e in b)b[e]="";A.value.forEach((e=>{e.updateMessage()})),M.value.forEach((e=>{e.updateMessage()}))},_e=()=>{if(localStorage.getItem("yudaoToken"))return L(),void oe();const e=a.get("userInfo");if(e){const a=JSON.parse(e);try{F({userId:a.userId,userName:a.userName,realName:a.realName,avatar:a.avatar,plaintextUserId:a.plaintextUserId,mobile:a.mobile,email:a.email}).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),L(),oe())}))}catch(t){}}};return(e,a)=>{const t=k,l=X("v-md-preview"),m=ee;return o(),i("div",Sa,[n("div",Ca,[s(te)?(o(),i(c,{key:0},[n("div",Ta,[(o(!0),i(c,null,v(s(I),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(Xe,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:s(b)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>s(b)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRef",ref:A},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",$a,[p(t,{onClick:Ie},{default:d((()=>a[1]||(a[1]=[f("Clear")]))),_:1}),p(t,{onClick:de,loading:s(ne),type:"primary"},{default:d((()=>a[2]||(a[2]=[f("Execute")]))),_:1},8,["loading"])])]),V((o(),i("div",{style:{width:"60%","margin-left":"15px",padding:"15px","border-radius":"10px"},class:R({"bg-[#fff]":s(N)})},[n("div",Ba,[s(N)?(o(),u(l,{key:0,text:s(N),id:"previewMd"},null,8,["text"])):h("",!0)]),n("div",null,[s(re)?(o(),i("img",{key:0,src:s(g),alt:"loading",class:"spinner"},null,8,ja)):h("",!0),s(re)?(o(),i("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:U},r(e.$t("tool.stopGeneration")),1)):h("",!0),s(se)?(o(),i("img",{key:2,onClick:ke,src:s(y),alt:"",style:{width:"20px"},class:"copy"},null,8,za)):h("",!0)])],2)),[[m,s(ie)]])],64)):h("",!0)]),n("div",qa,[p(s(_a),{active:s(z),shrink:"","line-width":"20"},{default:d((()=>[p(s(Ia),{title:"输入",name:"a"},{default:d((()=>[(o(!0),i(c,null,v(s(I),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(Xe,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:s(b)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>s(b)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRefs",ref:M},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Na,[p(t,{onClick:Ie},{default:d((()=>a[3]||(a[3]=[f("Clear")]))),_:1}),p(t,{onClick:a[0]||(a[0]=e=>de()),loading:s(ne),type:"primary"},{default:d((()=>a[4]||(a[4]=[f("Execute")]))),_:1},8,["loading"])])])),_:1}),p(s(Ia),{title:"结果",name:"b"},{default:d((()=>[n("div",Oa,[s(N)?(o(),u(l,{key:0,text:s(N),id:"previewMd"},null,8,["text"])):h("",!0)])])),_:1})])),_:1},8,["active"])])])}}},[["__scopeId","data-v-2a432ee7"]]);export{Ra as default};
