var e;import{aW as r,x as o,$ as s,aX as t,aY as a,a3 as d}from"https://static.medsci.cn/ai-write/static/index-6e66f8c2.js";window.$decode=r;const n=o.get("userInfo")?null==(e=JSON.parse(o.get("userInfo")))?void 0:e.userId:"",i=s.create({baseURL:"https://ai.medon.com.cn",timeout:12e4,headers:{"Content-Type":"application/json;charset=utf-8"}});i.interceptors.request.use((e=>{const r=t.get("deviceId")||null;return e.headers["Visitor-Code"]=r,e.headers["User-Id"]=n,"get"===e.method&&(e.params=e.data),!e.noLoading&&a.showLoading(),e}),(e=>(console.error(e),Promise.reject(e)))),i.interceptors.response.use((e=>{const{status:o,data:s}=e;if(200===o){a.hideLoading();if(0===s.code){if(console.log(s,"-我是"),s.data){let e=s.data.split("").reverse().join("");return console.log("decode==>",r(e)),r(e).data&&"error"!=r(e).data.type&&"请充值会员"!=r(e).data&&200==r(e).code?Promise.resolve(r(e)):(d.error("访问太火爆了！请稍后再试~"),Promise.reject("访问太火爆了！请稍后再试~"))}return d.error("访问太火爆了！请稍后再试~"),Promise.reject("访问太火爆了！请稍后再试~")}console.error(s),Promise.reject(s),d.error(s.msg)}else d.error("访问太火爆了！请稍后再试~"),Promise.reject("访问太火爆了！请稍后再试~")}),(e=>(console.error(e),a.hideLoading(),Promise.reject(e))));const c=i.request,p=(e,r)=>c({url:`/paper/search/${e}`,method:"post",data:r}),l=e=>c({url:"/paper/rewrite/sentence",method:"post",data:e}),m=(e,r)=>c({url:`/paper/${e}`,method:"post",data:r});export{p as a,m,l as s};
