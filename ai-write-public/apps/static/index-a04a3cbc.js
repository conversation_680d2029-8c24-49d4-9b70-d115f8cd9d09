/* empty css                  *//* empty css                */import{r as e,z as a,u as t,w as l,d as o,e as i,j as n,t as s,g as r,h as u,m as d,F as c,A as v,f as p,B as f,k as m,i as g,C as h,D as y,G as b,H as w,I as x,E as _,J as k,K as I,L as S,M as $,N as C,O as T,P as B,Q as z,R as j,o as q,S as N,T as A,U,V,W as O,X as R,a as E,b as M,Y as L,Z as H,$ as D,a0 as W,a1 as J,a2 as P,c as F,a3 as X,a4 as Z,p as Y,a5 as G,a6 as K,a7 as Q,a8 as ee,a9 as ae,aa as te,ab as le,x as oe,y as ie}from"./index-51e6c9ce.js";import{g as ne}from"./index-0bce936e.js";/* empty css                 *//* empty css                  *//* empty css                  */import{c as se,r as re,g as ue,s as de,i as ce,o as ve,a as pe,n as fe,m as me,b as ge,u as he,d as ye,e as be,f as we,h as xe,j as _e,k as ke,w as Ie,l as Se,p as $e,t as Ce,q as Te,v as Be,x as ze,y as je,z as qe,A as Ne,B as Ae,C as Ue,D as Ve,E as Oe,F as Re,G as Ee,H as Me,I as Le,J as He,K as De,L as We,M as Je,N as Pe}from"./use-touch-33637088.js";import{r as Fe,a as Xe,f as Ze}from"./use-route-829b0cd7.js";const Ye={class:"p-3 flex-1 rounded-md"},Ge={class:"text-[14px] font-bold mb-2 text-gray-600"},Ke={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(I,{expose:S,emit:$}){const C=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),T=t(),B=e([]),z=e({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),j=e(""),q=I,N=q.type,A=q.fileVerify,U=q.label,V=q.required,O=q.max_length,R=q.options;"file"==N&&(j.value=null),"file-list"==N&&(j.value=[]);const E={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},M=()=>{let e="";return A.forEach(((a,t)=>{t<A.length-1?e+=E[a].join(",")+",":e+=E[a].join(",")})),e},L=$,H=(e,a,t)=>{},D=()=>{j.value=""},W=async e=>{const{file:a,onSuccess:t,onError:l}=e,o=new FormData;o.append("file",a),o.append("appId",T.params.uuid),o.append("user",C.value.userName);try{const e=await h(o);"file-list"==N?j.value.push({type:z.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):j.value={type:z.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},t(e,a)}catch(i){l(i)}return!1};R&&R.length>0&&(j.value=R[0]);return S({updateMessage:()=>{R&&R.length>0?j.value=R[0]:"file"==N?(j.value=null,B.value=[]):"file-list"==N?(j.value=[],B.value=[]):j.value=""}}),l(j,(e=>{L("update:value",e)}),{immediate:!0,deep:!0}),(e,a)=>{const t=y,l=b,h=w,I=x,S=_,$=k;return o(),i("div",Ye,[n("div",Ge,s(r(U)),1),"paragraph"===r(N)||"text-input"===r(N)?(o(),u(t,{key:0,modelValue:j.value,"onUpdate:modelValue":a[0]||(a[0]=e=>j.value=e),type:"paragraph"===r(N)?"textarea":"text",rows:5,required:r(V),placeholder:`${r(U)}`,"show-word-limit":"",resize:"none",maxlength:r(O)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===r(N)?(o(),u(t,{key:1,modelValue:j.value,"onUpdate:modelValue":a[1]||(a[1]=e=>j.value=e),modelModifiers:{number:!0},type:"number",required:r(V),placeholder:`${r(U)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===r(N)?(o(),u(h,{key:2,modelValue:j.value,"onUpdate:modelValue":a[2]||(a[2]=e=>j.value=e),required:r(V),placeholder:`${r(U)}`},{default:d((()=>[(o(!0),i(c,null,v(r(R),(e=>(o(),u(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===r(N)||"file-list"===r(N)?(o(),u($,{key:3,"file-list":B.value,"onUpdate:fileList":a[3]||(a[3]=e=>B.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":D,"before-remove":e.beforeRemove,limit:r(O),accept:M(),"auto-upload":!0,"on-Success":H,"http-request":W,"on-exceed":e.handleExceed},{default:d((()=>[p(S,{disabled:B.value.length==r(O)},{default:d((()=>[p(I,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:d((()=>[p(r(f))])),_:1}),m("从本地上传")])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):g("",!0)])}}},Qe=Array.isArray,ea=e=>"string"==typeof e,aa=e=>null!==e&&"object"==typeof e,ta=/\B([A-Z])/g,la=(e=>{const a=Object.create(null);return t=>a[t]||(a[t]=e(t))})((e=>e.replace(ta,"-$1").toLowerCase()));function oa(e){if(Qe(e)){const a={};for(let t=0;t<e.length;t++){const l=e[t],o=ea(l)?ra(l):oa(l);if(o)for(const e in o)a[e]=o[e]}return a}if(ea(e)||aa(e))return e}const ia=/;(?![^(]*\))/g,na=/:([^]+)/,sa=/\/\*[^]*?\*\//g;function ra(e){const a={};return e.replace(sa,"").split(ia).forEach((e=>{if(e){const t=e.split(na);t.length>1&&(a[t[0].trim()]=t[1].trim())}})),a}function ua(e){let a="";if(ea(e))a=e;else if(Qe(e))for(let t=0;t<e.length;t++){const l=ua(e[t]);l&&(a+=l+" ")}else if(aa(e))for(const t in e)e[t]&&(a+=t+" ");return a.trim()}let da=0;function ca(){const e=I(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++da}`}function va(e,a){if(!ce||!window.IntersectionObserver)return;const t=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&t.unobserve(e.value)};$(l),C(l),ve((()=>{e.value&&t.observe(e.value)}))}const[pa,fa]=pe("sticky");const ma=$e(T({name:pa,props:{zIndex:fe,position:me("top"),container:Object,offsetTop:ge(0),offsetBottom:ge(0)},emits:["scroll","change"],setup(a,{emit:t,slots:o}){const i=e(),n=he(i),s=B({fixed:!1,width:0,height:0,transform:0}),r=e(!1),u=z((()=>ye("top"===a.position?a.offsetTop:a.offsetBottom))),d=z((()=>{if(r.value)return;const{fixed:e,height:a,width:t}=s;return e?{width:`${t}px`,height:`${a}px`}:void 0})),c=z((()=>{if(!s.fixed||r.value)return;const e=be(we(a.zIndex),{width:`${s.width}px`,height:`${s.height}px`,[a.position]:`${u.value}px`});return s.transform&&(e.transform=`translate3d(0, ${s.transform}px, 0)`),e})),v=()=>{if(!i.value||_e(i))return;const{container:e,position:l}=a,o=ke(i),n=ue(window);if(s.width=o.width,s.height=o.height,"top"===l)if(e){const a=ke(e),t=a.bottom-u.value-s.height;s.fixed=u.value>o.top&&a.bottom>0,s.transform=t<0?t:0}else s.fixed=u.value>o.top;else{const{clientHeight:a}=document.documentElement;if(e){const t=ke(e),l=a-t.top-u.value-s.height;s.fixed=a-u.value<o.bottom&&a>t.top,s.transform=l<0?-l:0}else s.fixed=a-u.value<o.bottom}(e=>{t("scroll",{scrollTop:e,isFixed:s.fixed})})(n)};return l((()=>s.fixed),(e=>t("change",e))),xe("scroll",v,{target:n,passive:!0}),va(i,v),l([Ie,Se],(()=>{i.value&&!_e(i)&&s.fixed&&(r.value=!0,j((()=>{const e=ke(i);s.width=e.width,s.height=e.height,r.value=!1})))})),()=>{var e;return p("div",{ref:i,style:d.value},[p("div",{class:fa({fixed:s.fixed&&!r.value}),style:c.value},[null==(e=o.default)?void 0:e.call(o)])])}}})),[ga,ha]=pe("swipe"),ya={loop:Ce,width:fe,height:fe,vertical:Boolean,autoplay:ge(0),duration:ge(500),touchable:Ce,lazyRender:Boolean,initialSwipe:ge(0),indicatorColor:String,showIndicators:Ce,stopPropagation:Ce},ba=Symbol(ga);const wa=$e(T({name:ga,props:ya,emits:["change","dragStart","dragEnd"],setup(a,{emit:t,slots:o}){const i=e(),n=e(),s=B({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let r=!1;const u=Te(),{children:d,linkChildren:c}=Be(ba),v=z((()=>d.length)),f=z((()=>s[a.vertical?"height":"width"])),m=z((()=>a.vertical?u.deltaY.value:u.deltaX.value)),g=z((()=>{if(s.rect){return(a.vertical?s.rect.height:s.rect.width)-f.value*v.value}return 0})),h=z((()=>f.value?Math.ceil(Math.abs(g.value)/f.value):v.value)),y=z((()=>v.value*f.value)),b=z((()=>(s.active+v.value)%v.value)),w=z((()=>{const e=a.vertical?"vertical":"horizontal";return u.direction.value===e})),x=z((()=>{const e={transitionDuration:`${s.swiping?0:a.duration}ms`,transform:`translate${a.vertical?"Y":"X"}(${+s.offset.toFixed(2)}px)`};if(f.value){const t=a.vertical?"height":"width",l=a.vertical?"width":"height";e[t]=`${y.value}px`,e[l]=a[l]?`${a[l]}px`:""}return e})),_=(e,t=0)=>{let l=e*f.value;a.loop||(l=Math.min(l,-g.value));let o=t-l;return a.loop||(o=Ue(o,g.value,0)),o},k=({pace:e=0,offset:l=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=s,n=(e=>{const{active:t}=s;return e?a.loop?Ue(t+e,-1,v.value):Ue(t+e,0,h.value):t})(e),r=_(n,l);if(a.loop){if(d[0]&&r!==g.value){const e=r<g.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==r){const e=r>0;d[v.value-1].setOffset(e?-y.value:0)}}s.active=n,s.offset=r,o&&n!==i&&t("change",b.value)},I=()=>{s.swiping=!0,s.active<=-1?k({pace:v.value}):s.active>=v.value&&k({pace:-v.value})},S=()=>{I(),u.reset(),Ne((()=>{s.swiping=!1,k({pace:1,emitChange:!0})}))};let T;const A=()=>clearTimeout(T),U=()=>{A(),+a.autoplay>0&&v.value>1&&(T=setTimeout((()=>{S(),U()}),+a.autoplay))},V=(e=+a.initialSwipe)=>{if(!i.value)return;const t=()=>{var t,l;if(!_e(i)){const e={width:i.value.offsetWidth,height:i.value.offsetHeight};s.rect=e,s.width=+(null!=(t=a.width)?t:e.width),s.height=+(null!=(l=a.height)?l:e.height)}v.value&&-1===(e=Math.min(v.value-1,e))&&(e=v.value-1),s.active=e,s.swiping=!0,s.offset=_(e),d.forEach((e=>{e.setOffset(0)})),U()};_e(i)?j().then(t):t()},O=()=>V(s.active);let R;const E=e=>{!a.touchable||e.touches.length>1||(u.start(e),r=!1,R=Date.now(),A(),I())},M=()=>{if(!a.touchable||!s.swiping)return;const e=Date.now()-R,l=m.value/e;if((Math.abs(l)>.25||Math.abs(m.value)>f.value/2)&&w.value){const e=a.vertical?u.offsetY.value:u.offsetX.value;let t=0;t=a.loop?e>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/f.value),k({pace:t,emitChange:!0})}else m.value&&k({pace:0});r=!1,s.swiping=!1,t("dragEnd",{index:b.value}),U()},L=(e,t)=>{const l=t===b.value,o=l?{backgroundColor:a.indicatorColor}:void 0;return p("i",{style:o,class:ha("indicator",{active:l})},null)};return ze({prev:()=>{I(),u.reset(),Ne((()=>{s.swiping=!1,k({pace:-1,emitChange:!0})}))},next:S,state:s,resize:O,swipeTo:(e,t={})=>{I(),u.reset(),Ne((()=>{let l;l=a.loop&&e===v.value?0===s.active?0:e:e%v.value,t.immediate?Ne((()=>{s.swiping=!1})):s.swiping=!1,k({pace:l-s.active,emitChange:!0})}))}}),c({size:f,props:a,count:v,activeIndicator:b}),l((()=>a.initialSwipe),(e=>V(+e))),l(v,(()=>V(s.active))),l((()=>a.autoplay),U),l([Ie,Se,()=>a.width,()=>a.height],O),l(je(),(e=>{"visible"===e?U():A()})),q(V),N((()=>V(s.active))),qe((()=>V(s.active))),$(A),C(A),xe("touchmove",(e=>{if(a.touchable&&s.swiping&&(u.move(e),w.value)){!a.loop&&(0===s.active&&m.value>0||s.active===v.value-1&&m.value<0)||(Ae(e,a.stopPropagation),k({offset:m.value}),r||(t("dragStart",{index:b.value}),r=!0))}}),{target:n}),()=>{var e;return p("div",{ref:i,class:ha()},[p("div",{ref:n,style:x.value,class:ha("track",{vertical:a.vertical}),onTouchstartPassive:E,onTouchend:M,onTouchcancel:M},[null==(e=o.default)?void 0:e.call(o)]),o.indicator?o.indicator({active:b.value,total:v.value}):a.showIndicators&&v.value>1?p("div",{class:ha("indicators",{vertical:a.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[xa,_a]=pe("tabs");var ka=T({name:xa,props:{count:Ve(Number),inited:Boolean,animated:Boolean,duration:Ve(fe),swipeable:Boolean,lazyRender:Boolean,currentIndex:Ve(Number)},emits:["change"],setup(a,{emit:t,slots:o}){const i=e(),n=e=>t("change",e),s=()=>{var e;const t=null==(e=o.default)?void 0:e.call(o);return a.animated||a.swipeable?p(wa,{ref:i,loop:!1,class:_a("track"),duration:1e3*+a.duration,touchable:a.swipeable,lazyRender:a.lazyRender,showIndicators:!1,onChange:n},{default:()=>[t]}):t},r=e=>{const t=i.value;t&&t.state.active!==e&&t.swipeTo(e,{immediate:!a.inited})};return l((()=>a.currentIndex),r),q((()=>{r(a.currentIndex)})),ze({swipeRef:i}),()=>p("div",{class:_a("content",{animated:a.animated||a.swipeable})},[s()])}});const[Ia,Sa]=pe("tabs"),$a={type:me("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:ge(0),duration:ge(.3),animated:Boolean,ellipsis:Ce,swipeable:Boolean,scrollspy:Boolean,offsetTop:ge(0),background:String,lazyRender:Ce,showHeader:Ce,lineWidth:fe,lineHeight:fe,beforeChange:Function,swipeThreshold:ge(5),titleActiveColor:String,titleInactiveColor:String},Ca=Symbol(Ia);var Ta=T({name:Ia,props:$a,emits:["change","scroll","rendered","clickTab","update:active"],setup(a,{emit:t,slots:o}){let i,n,s,r,u;const d=e(),c=e(),v=e(),f=e(),m=ca(),g=he(d),[h,y]=function(){const a=e([]),t=[];return S((()=>{a.value=[]})),[a,e=>(t[e]||(t[e]=t=>{a.value[e]=t}),t[e])]}(),{children:b,linkChildren:w}=Be(Ca),x=B({inited:!1,position:"",lineStyle:{},currentIndex:-1}),_=z((()=>b.length>+a.swipeThreshold||!a.ellipsis||a.shrink)),k=z((()=>({borderColor:a.color,background:a.background}))),I=(e,a)=>{var t;return null!=(t=e.name)?t:a},$=z((()=>{const e=b[x.currentIndex];if(e)return I(e,x.currentIndex)})),C=z((()=>ye(a.offsetTop))),T=z((()=>a.sticky?C.value+i:0)),q=e=>{const t=c.value,l=h.value;if(!(_.value&&t&&l&&l[x.currentIndex]))return;const o=l[x.currentIndex].$el,i=o.offsetLeft-(t.offsetWidth-o.offsetWidth)/2;r&&r(),r=function(e,a,t){let l,o=0;const i=e.scrollLeft,n=0===t?1:Math.round(1e3*t/16);let s=i;return function t(){s+=(a-i)/n,e.scrollLeft=s,++o<n&&(l=re(t))}(),function(){se(l)}}(t,i,e?0:+a.duration)},A=()=>{const e=x.inited;j((()=>{const t=h.value;if(!t||!t[x.currentIndex]||"line"!==a.type||_e(d.value))return;const l=t[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=a,n=l.offsetLeft+l.offsetWidth/2,s={width:Oe(o),backgroundColor:a.color,transform:`translateX(${n}px) translateX(-50%)`};if(e&&(s.transitionDuration=`${a.duration}s`),Re(i)){const e=Oe(i);s.height=e,s.borderRadius=e}x.lineStyle=s}))},U=(e,l)=>{const o=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(e);if(!Re(o))return;const i=b[o],n=I(i,o),r=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,l||q(),A()),n!==a.active&&(t("update:active",n),r&&t("change",n,i.title)),s&&!a.scrollspy&&Me(Math.ceil(Le(d.value)-C.value))},V=(e,a)=>{const t=b.find(((a,t)=>I(a,t)===e)),l=t?b.indexOf(t):0;U(l,a)},O=(e=!1)=>{if(a.scrollspy){const t=b[x.currentIndex].$el;if(t&&g.value){const l=Le(t,g.value)-T.value;n=!0,u&&u(),u=function(e,a,t,l){let o,i=ue(e);const n=i<a,s=0===t?1:Math.round(1e3*t/16),r=(a-i)/s;return function t(){i+=r,(n&&i>a||!n&&i<a)&&(i=a),de(e,i),n&&i<a||!n&&i>a?o=re(t):l&&(o=re(l))}(),function(){se(o)}}(g.value,l,e?0:+a.duration,(()=>{n=!1}))}}},R=(e,l,o)=>{const{title:i,disabled:n}=b[l],s=I(b[l],l);n||(He(a.beforeChange,{args:[s],done:()=>{U(l),O()}}),Fe(e)),t("clickTab",{name:s,title:i,event:o,disabled:n})},E=e=>{s=e.isFixed,t("scroll",e)},M=()=>{if("line"===a.type&&b.length)return p("div",{class:Sa("line"),style:x.lineStyle},null)},L=()=>{var e,t,l;const{type:i,border:n,sticky:s}=a,r=[p("div",{ref:s?void 0:v,class:[Sa("wrap"),{[Ee]:"line"===i&&n}]},[p("div",{ref:c,role:"tablist",class:Sa("nav",[i,{shrink:a.shrink,complete:_.value}]),style:k.value,"aria-orientation":"horizontal"},[null==(e=o["nav-left"])?void 0:e.call(o),b.map((e=>e.renderTitle(R))),M(),null==(t=o["nav-right"])?void 0:t.call(o)])]),null==(l=o["nav-bottom"])?void 0:l.call(o)];return s?p("div",{ref:v},[r]):r},H=()=>{A(),j((()=>{var e,a;q(!0),null==(a=null==(e=f.value)?void 0:e.swipeRef.value)||a.resize()}))};l((()=>[a.color,a.duration,a.lineWidth,a.lineHeight]),A),l(Ie,H),l((()=>a.active),(e=>{e!==$.value&&V(e)})),l((()=>b.length),(()=>{x.inited&&(V(a.active),A(),j((()=>{q(!0)})))}));return ze({resize:H,scrollTo:e=>{j((()=>{V(e),O(!0)}))}}),N(A),qe(A),ve((()=>{V(a.active,!0),j((()=>{x.inited=!0,v.value&&(i=ke(v.value).height),q(!0)}))})),va(d,A),xe("scroll",(()=>{if(a.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=ke(b[e].$el);if(a>T.value)return 0===e?0:e-1}return b.length-1})();U(e)}}),{target:g,passive:!0}),w({id:m,props:a,setLine:A,scrollable:_,onRendered:(e,a)=>t("rendered",e,a),currentName:$,setTitleRefs:y,scrollIntoView:q}),()=>p("div",{ref:d,class:Sa([a.type])},[a.showHeader?a.sticky?p(ma,{container:d.value,offsetTop:C.value,onScroll:E},{default:()=>[L()]}):L():null,p(ka,{ref:f,count:b.length,inited:x.inited,animated:a.animated,duration:a.duration,swipeable:a.swipeable,lazyRender:a.lazyRender,currentIndex:x.currentIndex,onChange:U},{default:()=>{var e;return[null==(e=o.default)?void 0:e.call(o)]}})])}});const Ba=Symbol(),[za,ja]=pe("tab"),qa=T({name:za,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:fe,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:Ce},setup(e,{slots:a}){const t=z((()=>{const a={},{type:t,color:l,disabled:o,isActive:i,activeColor:n,inactiveColor:s}=e;l&&"card"===t&&(a.borderColor=l,o||(i?a.backgroundColor=l:a.color=l));const r=i?n:s;return r&&(a.color=r),a})),l=()=>{const t=p("span",{class:ja("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||Re(e.badge)&&""!==e.badge?p(De,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[t]}):t};return()=>p("div",{id:e.id,role:"tab",class:[ja([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:t.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[Na,Aa]=pe("swipe-item");const Ua=$e(T({name:Na,setup(e,{slots:a}){let t;const l=B({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=We(ba);if(!o)return;const n=z((()=>{const e={},{vertical:a}=o.props;return o.size.value&&(e[a?"height":"width"]=`${o.size.value}px`),l.offset&&(e.transform=`translate${a?"Y":"X"}(${l.offset}px)`),e})),s=z((()=>{const{loop:e,lazyRender:a}=o.props;if(!a||t)return!0;if(!l.mounted)return!1;const n=o.activeIndicator.value,s=o.count.value-1,r=0===n&&e?s:n-1,u=n===s&&e?0:n+1;return t=i.value===n||i.value===r||i.value===u,t}));return q((()=>{j((()=>{l.mounted=!0}))})),ze({setOffset:e=>{l.offset=e}}),()=>{var e;return p("div",{class:Aa(),style:n.value},[s.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[Va,Oa]=pe("tab");const Ra=$e(T({name:Va,props:be({},Xe,{dot:Boolean,name:fe,badge:fe,title:String,disabled:Boolean,titleClass:Je,titleStyle:[String,Object],showZeroBadge:Ce}),setup(a,{slots:t}){const o=ca(),i=e(!1),n=I(),{parent:s,index:r}=We(Ca);if(!s)return;const u=()=>{var e;return null!=(e=a.name)?e:r.value},d=z((()=>{const e=u()===s.currentName.value;return e&&!i.value&&(i.value=!0,s.props.lazyRender&&j((()=>{s.onRendered(u(),a.title)}))),e})),c=e(""),v=e("");A((()=>{const{titleClass:e,titleStyle:t}=a;c.value=e?ua(e):"",v.value=t&&"string"!=typeof t?function(e){let a="";if(!e||ea(e))return a;for(const t in e){const l=e[t];(ea(l)||"number"==typeof l)&&(a+=`${t.startsWith("--")?t:la(t)}:${l};`)}return a}(oa(t)):t}));const f=e(!d.value);return l(d,(e=>{e?f.value=!1:Ne((()=>{f.value=!0}))})),l((()=>a.title),(()=>{s.setLine(),s.scrollIntoView()})),U(Ba,d),ze({id:o,renderTitle:e=>p(qa,R({key:o,id:`${s.id}-${r.value}`,ref:s.setTitleRefs(r.value),style:v.value,class:c.value,isActive:d.value,controls:o,scrollable:s.scrollable.value,activeColor:s.props.titleActiveColor,inactiveColor:s.props.titleInactiveColor,onClick:a=>e(n.proxy,r.value,a)},Pe(s.props,["type","color","shrink"]),Pe(a,["dot","badge","title","disabled","showZeroBadge"])),{title:t.title})}),()=>{var e;const a=`${s.id}-${r.value}`,{animated:l,swipeable:n,scrollspy:u,lazyRender:c}=s.props;if(!t.default&&!l)return;const v=u||d.value;if(l||n)return p(Ua,{id:o,role:"tabpanel",class:Oa("panel-wrapper",{inactive:f.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":a,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[p("div",{class:Oa("panel")},[null==(e=t.default)?void 0:e.call(t)])]}});const m=i.value||u||!c?null==(e=t.default)?void 0:e.call(t):null;return V(p("div",{id:o,role:"tabpanel",class:Oa("panel"),tabindex:v?0:-1,"aria-labelledby":a,"data-allow-mismatch":"attribute"},[m]),[[O,v]])}}})),Ea=$e(Ta),Ma={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},La={class:"pc_container",style:{display:"flex"}},Ha={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},Da={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Wa={class:"pc_right bg-[#fff]"},Ja={id:"typing-area"},Pa={key:0,class:"decContaniner nop bg-[#fff]"},Fa={key:0,class:"img_box"},Xa=["src"],Za={key:1,class:"icon"},Ya={class:"process_text label_width"},Ga={key:0,class:"process"},Ka={key:0,class:"img_box"},Qa=["src"],et={key:1,class:"icon"},at={class:"process"},tt={class:"process_text"},lt=["src"],ot=["src"],it={class:"mobile_container"},nt={class:"p-3",style:{display:"flex","justify-content":"space-between"}},st={class:"mobile_right"},rt={id:"typing-area"},ut={key:0,class:"decContaniner nop bg-[#fff]"},dt={key:0,class:"img_box"},ct=["src"],vt={key:1,class:"icon"},pt={class:"process_text label_width"},ft=(e=>(oe("data-v-0badb70d"),e=e(),ie(),e))((()=>n("div",null,[n("div",{class:"process"})],-1))),mt={key:0,class:"img_box"},gt=["src"],ht={key:1,class:"icon"},yt={class:"process"},bt={class:"process_text"},wt=E({__name:"index",setup(f){const h=ne("loading.png"),y=ne("copy.png"),b=B({}),w=t(),k={},I=e([]),S=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),$=e(null),{t:C,locale:T}=M(),j=L(),N=e(!1);let A=e("a"),U=e("");const V=e(""),O=e(null),R=e(null),E=e(["1","2"]),oe=e(!1),ie=e(!1);let se;const re=async()=>{var e;await G({appId:w.params.uuid,user:S.value.userName,mode:null==(e=$.value)?void 0:e.mode,task_id:V.value}),setTimeout((()=>{Be.abort(),ze=!0,be.value=[],oe.value=!1,we.value.length&&we.value.forEach((e=>{e.status=!0})),Ve()}),0)};q((async()=>{var e,t;const l=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${l}px`);if(["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=w.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=w.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),T.value=H(),a.get("userInfo"))await He(),he(),await W(w.params.appUuid);else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=D();e&&"zh-CN"!=e?j.push("/login"):window.addLoginDom()}})),l(U,(()=>{U.value&&(A.value="b")}));const ue=()=>{w.params.uuid&&K({appId:w.params.uuid,user:S.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(I.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],t=e[a].variable;k[t]={label:e[a].label},b[t]=""})))}))},de=z((()=>!!I.value.length)),ce=z((()=>I.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),ve=()=>{w.params.uuid&&Q({appId:w.params.uuid,user:S.value.userName}).then((e=>{$.value={...e}}))},pe=e(!1),fe=e(!1),me=e(!1),ge=e(!1),he=async()=>{var e,a;let t=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=S.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:w.params.uuid,userUuid:null==(a=S.value)?void 0:a.openid}];await J.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",t)},ye=()=>{var e,a;if(0!=ce.value.length||(t=b,Object.values(t).some((e=>e)))){var t;for(let e in b)if(ce.value.includes(e)&&!b[e])return void ee({message:`${k[e].label}${C("tool.requiredfield")}`,type:"error"});(null==(e=$.value)?void 0:e.mode)&&(["advanced-chat","chat"].includes(null==(a=$.value)?void 0:a.mode)?ee({type:"success",message:C("tool.planning")}):"completion"==$.value.mode?Ue():Ae())}else ee({message:`${C("tool.enterquestion")}`,type:"error"})},be=e([]);var we=e([]),xe=e([]);const _e=e(""),ke=e(0),Ie=e(""),Se=e(!1);let $e=e(0);const Ce=e(!1),Te=e(!1);let Be,ze=!1,je=!1;l(we,(()=>{qe()}),{deep:!0});const qe=()=>{$e.value<we.value.length&&(xe.value.push(we.value[$e.value]),$e.value++,setTimeout(qe,1e3))},Ne=()=>{ke.value<_e.value.length?(Se.value=!0,Ie.value+=_e.value.charAt(ke.value),ke.value++,setTimeout(Ne,20)):(Te.value=!1,Se.value=!1,me.value=!0,Ve())},Ae=async()=>{ie.value=!0,oe.value=!0,U.value="",we.value=[],xe.value=[],$e.value=0,Ie.value="",_e.value="",be.value=[],Ce.value=!1,ze=!1,ke.value=0,Be=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run?locale=zh`;0,pe.value=!0,fe.value=!0,ge.value=!1,await Ze(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:S.value.userName,inputs:{...b,outputLanguage:b.outputLanguage?b.outputLanguage:"中文"==ae()?"简体中文":ae()},requestId:crypto.randomUUID(),files:[],response_mode:"streaming",appUuid:w.params.appUuid}),onmessage(e){var a,t,l,o,i,n,s;if(e.data.trim())try{const r=JSON.parse(e.data);if(V.value=r.task_id,r.error)throw new Error(r.error);"智能体推理思维链"==(null==(a=null==r?void 0:r.data)?void 0:a.title)&&"node_finished"===r.event&&(Te.value=!0,_e.value=JSON.parse(r.data.outputs.text).text,Ne()),"node_started"!==r.event||Ce.value||"开始"==(null==(t=null==r?void 0:r.data)?void 0:t.title)||we.value.push({node_id:null==(l=null==r?void 0:r.data)?void 0:l.node_id,title:null==(o=null==r?void 0:r.data)?void 0:o.title,status:!1}),"error"===r.event&&(Ce.value=!0,oe.value=!1,ze=!0,A.value="b",Ee(error)),"node_finished"===r.event&&we.value.forEach((e=>{var a;e.node_id==(null==(a=null==r?void 0:r.data)?void 0:a.node_id)&&(e.status=!0)})),"text_chunk"===r.event&&(N.value=!0,Ce.value=!0,be.value.push(null==(i=null==r?void 0:r.data)?void 0:i.text),Te.value||Ve()),"workflow_started"===r.event&&(pe.value=!1),"workflow_finished"===r.event&&(Ce.value=!0,oe.value=!1,N.value||(be.value.push(null==(s=null==(n=null==r?void 0:r.data)?void 0:n.outputs)?void 0:s.text),Te.value||Ve()),ze=!0,A.value="b")}catch(r){Ee(r)}},onerror(e){Ee(e)},signal:Be.signal,openWhenHidden:!0})}catch(e){Ee()}},Ue=async()=>{U.value="",be.value=[],Te.value=!1,ze=!1,Be=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages?locale=zh`;0,pe.value=!0,fe.value=!0,ge.value=!1,await Ze(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:S.value.userName,inputs:{...b,outputLanguage:ae()},files:[],response_mode:"streaming",appUuid:w.params.appUuid,requestId:crypto.randomUUID()}),onmessage(e){if(pe.value=!1,me.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(V.value=a.task_id,a.error)throw new Error(a.error);"error"===a.event&&(ze=!0,Ee(error)),"message"===a.event&&(be.value.push(null==a?void 0:a.answer),Te.value||Ve()),"message_end"===a.event&&(A.value="b",ze=!0)}catch(a){Ee(a)}},onerror(e){Ee(e)},signal:Be.signal,openWhenHidden:!0})}catch(e){Ee()}},Ve=()=>{if(0===be.value.length)return Te.value=!1,je=!0,void Oe();Te.value=!0;const e=be.value.shift();e&&Re(e).then((()=>{Ve()}))},Oe=()=>{je&&ze&&(fe.value=!1,me.value=!1,ge.value=!0)},Re=e=>new Promise((a=>{let t=0;se=setInterval((()=>{if(t<e.length){U.value+=e[t++];const a=document.getElementsByClassName("pc_right");a[0].scrollTop=a[0].scrollHeight;const l=document.getElementsByClassName("mobile_right");l[0].scrollTop=l[0].scrollHeight}else clearInterval(se),a()}),15)})),Ee=()=>{setTimeout((()=>{Be.abort()}),0),pe.value=!1,me.value=!1,fe.value=!1,Te.value=!1,ee.error(C("tool.accessbusy")),U.value=C("tool.accessbusy")},Me=async()=>{try{await navigator.clipboard.writeText(U.value),ee({type:"success",message:C("tool.copysuccess")})}catch(e){ee(e)}},Le=()=>{for(let e in b)b[e]="";O.value.forEach((e=>{e.updateMessage()})),R.value.forEach((e=>{e.updateMessage()}))},He=async()=>{if(localStorage.getItem("yudaoToken"))return ue(),void ve();try{await P({userId:S.value.userId,userName:S.value.userName,realName:S.value.realName,avatar:S.value.avatar,plaintextUserId:S.value.plaintextUserId,mobile:S.value.mobile,email:S.value.email}).then((async e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),ue(),ve())}))}catch(e){}};return(e,a)=>{const t=_,l=x,f=te,w=le,k=F("v-md-preview");return o(),i("div",Ma,[n("div",La,[r(de)?(o(),i(c,{key:0},[n("div",Ha,[(o(!0),i(c,null,v(r(I),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(Ke,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:r(b)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>r(b)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRef",ref:O},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Da,[p(t,{onClick:Le},{default:d((()=>[m(s(e.$t("tool.clear")),1)])),_:1}),p(t,{onClick:ye,loading:r(fe),type:"primary"},{default:d((()=>[m(s(e.$t("tool.execute")),1)])),_:1},8,["loading"])])]),n("div",Wa,[n("div",Ja,[r(xe).length>0||r(Ie)||r(ie)?(o(),i("div",Pa,[p(w,{modelValue:r(E),"onUpdate:modelValue":a[0]||(a[0]=e=>Y(E)?E.value=e:null)},{default:d((()=>[p(f,{name:"1"},{title:d((()=>[r(oe)?(o(),i("div",Fa,[n("img",{src:r(h),alt:"loading"},null,8,Xa)])):(o(),i("div",Za,[p(l,null,{default:d((()=>[p(r(X))])),_:1})])),m(" "+s(e.$t("tool.execution_progress")),1)])),default:d((()=>[(o(!0),i(c,null,v(r(xe),((a,t)=>(o(),i("div",{key:t,class:"process"},[n("div",Ya,s(a.title),1),m("    "),n("span",{style:{color:"#36b15e"},class:Z(a.status?"":"loading-text")},s(a.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),n("div",null,[r(Ie)?(o(),i("div",Ga)):g("",!0)]),r(Ie)?(o(),u(f,{key:0,name:"2"},{title:d((()=>[r(Se)?(o(),i("div",Ka,[n("img",{src:r(h),alt:"loading"},null,8,Qa)])):(o(),i("div",et,[p(l,null,{default:d((()=>[p(r(X))])),_:1})])),m(" "+s(e.$t("tool.reasoning_process")),1)])),default:d((()=>[n("div",at,[n("div",tt,s(r(Ie)),1)])])),_:1})):g("",!0)])),_:1},8,["modelValue"])])):g("",!0),r(U)?(o(),u(k,{key:1,text:r(U),id:"previewMd"},null,8,["text"])):g("",!0),n("div",null,[r(me)?(o(),i("img",{key:0,src:r(h),alt:"loading",class:"spinner"},null,8,lt)):g("",!0),r(me)?(o(),i("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:re},s(e.$t("tool.stopGeneration")),1)):g("",!0),r(ge)?(o(),i("img",{key:2,onClick:Me,src:r(y),alt:"",style:{width:"20px"},class:"copy"},null,8,ot)):g("",!0)])])])],64)):g("",!0)]),n("div",it,[p(r(Ea),{active:r(A),shrink:"","line-width":"20"},{default:d((()=>[p(r(Ra),{title:"输入",name:"a"},{default:d((()=>[(o(!0),i(c,null,v(r(I),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(Ke,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:r(b)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>r(b)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRefs",ref:R},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",nt,[p(t,{onClick:Le},{default:d((()=>[m("Clear")])),_:1}),p(t,{onClick:a[1]||(a[1]=e=>ye()),loading:r(fe),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])])),_:1}),p(r(Ra),{title:"结果",name:"b"},{default:d((()=>[n("div",st,[n("div",rt,[r(we).length>0||r(Ie)?(o(),i("div",ut,[p(w,{modelValue:r(E),"onUpdate:modelValue":a[2]||(a[2]=e=>Y(E)?E.value=e:null)},{default:d((()=>[p(f,{name:"1"},{title:d((()=>[r(oe)?(o(),i("div",dt,[n("img",{src:r(h),alt:"loading"},null,8,ct)])):(o(),i("div",vt,[p(l,null,{default:d((()=>[p(r(X))])),_:1})])),m(" "+s(e.$t("tool.execution_progress")),1)])),default:d((()=>[(o(!0),i(c,null,v(r(we),((a,t)=>(o(),i("div",{key:t,class:"process"},[n("div",pt,s(a.title),1),m("    "),n("span",{style:{color:"#36b15e"},class:Z(a.status?"":"loading-text")},s(a.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),ft,r(Ie)?(o(),u(f,{key:0,title:"推导过程",name:"2"},{title:d((()=>[r(Se)?(o(),i("div",mt,[n("img",{src:r(h),alt:"loading"},null,8,gt)])):(o(),i("div",ht,[p(l,null,{default:d((()=>[p(r(X))])),_:1})])),m(" "+s(e.$t("tool.reasoning_process")),1)])),default:d((()=>[n("div",yt,[n("div",bt,s(r(Ie)),1)])])),_:1})):g("",!0)])),_:1},8,["modelValue"])])):g("",!0),r(U)?(o(),u(k,{key:1,text:r(U),id:"previewMd"},null,8,["text"])):g("",!0)])])])),_:1})])),_:1},8,["active"])])])}}},[["__scopeId","data-v-0badb70d"]]);export{wt as default};
