/* empty css                  *//* empty css                   */import{r as e,x as t,a,w as l,d as o,e as i,j as n,t as r,g as s,h as u,m as d,F as c,y as v,f as p,z as f,k as m,i as h,A as g,B as y,C as b,D as w,G as x,E as I,H as k,I as S,J as _,K as C,L as $,M as T,N as j,O as B,n as z,o as q,P as N,Q as O,R as A,S as R,T as U,U as V,b as E,u as M,V as L,W as H,X as W,Y as D,Z as J,c as P,$ as F,a0 as X,a1 as Z,a2 as Y,a3 as G,a4 as K}from"https://static.medsci.cn/ai-write/static/index-ce91ddf0.js";import{g as Q}from"./index-5d3359b3.js";/* empty css                *//* empty css                 *//* empty css                  *//* empty css                  */import{_ as ee}from"./_plugin-vue_export-helper-1b428a4d.js";import{c as te,r as ae,g as le,s as oe,i as ie,o as ne,a as re,n as se,m as ue,b as de,u as ce,d as ve,e as pe,f as fe,h as me,j as he,k as ge,w as ye,l as be,p as we,t as xe,q as Ie,v as ke,x as Se,y as _e,z as Ce,A as $e,B as Te,C as je,D as Be,E as ze,F as qe,G as Ne,H as Oe,I as Ae,J as Re,K as Ue,L as Ve,M as Ee,N as Me}from"./use-touch-c477848d.js";import{r as Le,a as He,f as We}from"./use-route-1ddc39e6.js";const De={class:"p-3 flex-1 rounded-md"},Je={class:"text-[14px] font-bold mb-2 text-gray-600"},Pe={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(S,{emit:_}){const C=e(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),$=a(),T=e([]),j=e({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),B=S,z=B.type,q=B.fileVerify,N=B.label,O=B.required,A=B.max_length,R=B.options,U={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},V=()=>{let e="";return q.forEach((t=>{e+=U[t].join(",")})),e},E=_,M=e(""),L=(e,t,a)=>{},H=()=>{M.value=""},W=async e=>{const{file:t,onSuccess:a,onError:l}=e,o=new FormData;o.append("file",t),o.append("appId",$.params.uuid),o.append("user",C.value.userName);try{const e=await g(o);M.value={type:j.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},a(e,t)}catch(i){l(i)}return!1};return l(M,(e=>{E("update:value",e)})),(e,t)=>{const a=y,l=b,g=w,S=x,_=I,C=k;return o(),i("div",De,[n("div",Je,r(s(N)),1),"paragraph"===s(z)||"text-input"===s(z)?(o(),u(a,{key:0,modelValue:M.value,"onUpdate:modelValue":t[0]||(t[0]=e=>M.value=e),type:"paragraph"===s(z)?"textarea":"text",rows:5,required:s(O),placeholder:`${s(N)}`,"show-word-limit":"",resize:"none",maxlength:s(A)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===s(z)?(o(),u(a,{key:1,modelValue:M.value,"onUpdate:modelValue":t[1]||(t[1]=e=>M.value=e),modelModifiers:{number:!0},type:"number",required:s(O),placeholder:`${s(N)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===s(z)?(o(),u(g,{key:2,modelValue:M.value,"onUpdate:modelValue":t[2]||(t[2]=e=>M.value=e),required:s(O),placeholder:`${s(N)}`},{default:d((()=>[(o(!0),i(c,null,v(s(R),(e=>(o(),u(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===s(z)?(o(),u(C,{key:3,"file-list":T.value,"onUpdate:fileList":t[3]||(t[3]=e=>T.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":H,"before-remove":e.beforeRemove,limit:1,accept:V(),"auto-upload":!0,"on-Success":L,"http-request":W,"on-exceed":e.handleExceed},{default:d((()=>[p(_,{disabled:1==T.value.length},{default:d((()=>[p(S,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:d((()=>[p(s(f))])),_:1}),m("从本地上传")])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","accept","on-exceed"])):h("",!0)])}}},Fe=Array.isArray,Xe=e=>"string"==typeof e,Ze=e=>null!==e&&"object"==typeof e,Ye=/\B([A-Z])/g,Ge=(e=>{const t=Object.create(null);return a=>t[a]||(t[a]=e(a))})((e=>e.replace(Ye,"-$1").toLowerCase()));function Ke(e){if(Fe(e)){const t={};for(let a=0;a<e.length;a++){const l=e[a],o=Xe(l)?at(l):Ke(l);if(o)for(const e in o)t[e]=o[e]}return t}if(Xe(e)||Ze(e))return e}const Qe=/;(?![^(]*\))/g,et=/:([^]+)/,tt=/\/\*[^]*?\*\//g;function at(e){const t={};return e.replace(tt,"").split(Qe).forEach((e=>{if(e){const a=e.split(et);a.length>1&&(t[a[0].trim()]=a[1].trim())}})),t}function lt(e){let t="";if(Xe(e))t=e;else if(Fe(e))for(let a=0;a<e.length;a++){const l=lt(e[a]);l&&(t+=l+" ")}else if(Ze(e))for(const a in e)e[a]&&(t+=a+" ");return t.trim()}let ot=0;function it(){const e=S(),{name:t="unknown"}=(null==e?void 0:e.type)||{};return`${t}-${++ot}`}function nt(e,t){if(!ie||!window.IntersectionObserver)return;const a=new IntersectionObserver((e=>{t(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&a.unobserve(e.value)};C(l),$(l),ne((()=>{e.value&&a.observe(e.value)}))}const[rt,st]=re("sticky");const ut=we(T({name:rt,props:{zIndex:se,position:ue("top"),container:Object,offsetTop:de(0),offsetBottom:de(0)},emits:["scroll","change"],setup(t,{emit:a,slots:o}){const i=e(),n=ce(i),r=j({fixed:!1,width:0,height:0,transform:0}),s=e(!1),u=B((()=>ve("top"===t.position?t.offsetTop:t.offsetBottom))),d=B((()=>{if(s.value)return;const{fixed:e,height:t,width:a}=r;return e?{width:`${a}px`,height:`${t}px`}:void 0})),c=B((()=>{if(!r.fixed||s.value)return;const e=pe(fe(t.zIndex),{width:`${r.width}px`,height:`${r.height}px`,[t.position]:`${u.value}px`});return r.transform&&(e.transform=`translate3d(0, ${r.transform}px, 0)`),e})),v=()=>{if(!i.value||he(i))return;const{container:e,position:l}=t,o=ge(i),n=le(window);if(r.width=o.width,r.height=o.height,"top"===l)if(e){const t=ge(e),a=t.bottom-u.value-r.height;r.fixed=u.value>o.top&&t.bottom>0,r.transform=a<0?a:0}else r.fixed=u.value>o.top;else{const{clientHeight:t}=document.documentElement;if(e){const a=ge(e),l=t-a.top-u.value-r.height;r.fixed=t-u.value<o.bottom&&t>a.top,r.transform=l<0?-l:0}else r.fixed=t-u.value<o.bottom}(e=>{a("scroll",{scrollTop:e,isFixed:r.fixed})})(n)};return l((()=>r.fixed),(e=>a("change",e))),me("scroll",v,{target:n,passive:!0}),nt(i,v),l([ye,be],(()=>{i.value&&!he(i)&&r.fixed&&(s.value=!0,z((()=>{const e=ge(i);r.width=e.width,r.height=e.height,s.value=!1})))})),()=>{var e;return p("div",{ref:i,style:d.value},[p("div",{class:st({fixed:r.fixed&&!s.value}),style:c.value},[null==(e=o.default)?void 0:e.call(o)])])}}})),[dt,ct]=re("swipe"),vt={loop:xe,width:se,height:se,vertical:Boolean,autoplay:de(0),duration:de(500),touchable:xe,lazyRender:Boolean,initialSwipe:de(0),indicatorColor:String,showIndicators:xe,stopPropagation:xe},pt=Symbol(dt);const ft=we(T({name:dt,props:vt,emits:["change","dragStart","dragEnd"],setup(t,{emit:a,slots:o}){const i=e(),n=e(),r=j({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let s=!1;const u=Ie(),{children:d,linkChildren:c}=ke(pt),v=B((()=>d.length)),f=B((()=>r[t.vertical?"height":"width"])),m=B((()=>t.vertical?u.deltaY.value:u.deltaX.value)),h=B((()=>{if(r.rect){return(t.vertical?r.rect.height:r.rect.width)-f.value*v.value}return 0})),g=B((()=>f.value?Math.ceil(Math.abs(h.value)/f.value):v.value)),y=B((()=>v.value*f.value)),b=B((()=>(r.active+v.value)%v.value)),w=B((()=>{const e=t.vertical?"vertical":"horizontal";return u.direction.value===e})),x=B((()=>{const e={transitionDuration:`${r.swiping?0:t.duration}ms`,transform:`translate${t.vertical?"Y":"X"}(${+r.offset.toFixed(2)}px)`};if(f.value){const a=t.vertical?"height":"width",l=t.vertical?"width":"height";e[a]=`${y.value}px`,e[l]=t[l]?`${t[l]}px`:""}return e})),I=(e,a=0)=>{let l=e*f.value;t.loop||(l=Math.min(l,-h.value));let o=a-l;return t.loop||(o=je(o,h.value,0)),o},k=({pace:e=0,offset:l=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=r,n=(e=>{const{active:a}=r;return e?t.loop?je(a+e,-1,v.value):je(a+e,0,g.value):a})(e),s=I(n,l);if(t.loop){if(d[0]&&s!==h.value){const e=s<h.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==s){const e=s>0;d[v.value-1].setOffset(e?-y.value:0)}}r.active=n,r.offset=s,o&&n!==i&&a("change",b.value)},S=()=>{r.swiping=!0,r.active<=-1?k({pace:v.value}):r.active>=v.value&&k({pace:-v.value})},_=()=>{S(),u.reset(),$e((()=>{r.swiping=!1,k({pace:1,emitChange:!0})}))};let T;const O=()=>clearTimeout(T),A=()=>{O(),+t.autoplay>0&&v.value>1&&(T=setTimeout((()=>{_(),A()}),+t.autoplay))},R=(e=+t.initialSwipe)=>{if(!i.value)return;const a=()=>{var a,l;if(!he(i)){const e={width:i.value.offsetWidth,height:i.value.offsetHeight};r.rect=e,r.width=+(null!=(a=t.width)?a:e.width),r.height=+(null!=(l=t.height)?l:e.height)}v.value&&-1===(e=Math.min(v.value-1,e))&&(e=v.value-1),r.active=e,r.swiping=!0,r.offset=I(e),d.forEach((e=>{e.setOffset(0)})),A()};he(i)?z().then(a):a()},U=()=>R(r.active);let V;const E=e=>{!t.touchable||e.touches.length>1||(u.start(e),s=!1,V=Date.now(),O(),S())},M=()=>{if(!t.touchable||!r.swiping)return;const e=Date.now()-V,l=m.value/e;if((Math.abs(l)>.25||Math.abs(m.value)>f.value/2)&&w.value){const e=t.vertical?u.offsetY.value:u.offsetX.value;let a=0;a=t.loop?e>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/f.value),k({pace:a,emitChange:!0})}else m.value&&k({pace:0});s=!1,r.swiping=!1,a("dragEnd",{index:b.value}),A()},L=(e,a)=>{const l=a===b.value,o=l?{backgroundColor:t.indicatorColor}:void 0;return p("i",{style:o,class:ct("indicator",{active:l})},null)};return Se({prev:()=>{S(),u.reset(),$e((()=>{r.swiping=!1,k({pace:-1,emitChange:!0})}))},next:_,state:r,resize:U,swipeTo:(e,a={})=>{S(),u.reset(),$e((()=>{let l;l=t.loop&&e===v.value?0===r.active?0:e:e%v.value,a.immediate?$e((()=>{r.swiping=!1})):r.swiping=!1,k({pace:l-r.active,emitChange:!0})}))}}),c({size:f,props:t,count:v,activeIndicator:b}),l((()=>t.initialSwipe),(e=>R(+e))),l(v,(()=>R(r.active))),l((()=>t.autoplay),A),l([ye,be,()=>t.width,()=>t.height],U),l(_e(),(e=>{"visible"===e?A():O()})),q(R),N((()=>R(r.active))),Ce((()=>R(r.active))),C(O),$(O),me("touchmove",(e=>{if(t.touchable&&r.swiping&&(u.move(e),w.value)){!t.loop&&(0===r.active&&m.value>0||r.active===v.value-1&&m.value<0)||(Te(e,t.stopPropagation),k({offset:m.value}),s||(a("dragStart",{index:b.value}),s=!0))}}),{target:n}),()=>{var e;return p("div",{ref:i,class:ct()},[p("div",{ref:n,style:x.value,class:ct("track",{vertical:t.vertical}),onTouchstartPassive:E,onTouchend:M,onTouchcancel:M},[null==(e=o.default)?void 0:e.call(o)]),o.indicator?o.indicator({active:b.value,total:v.value}):t.showIndicators&&v.value>1?p("div",{class:ct("indicators",{vertical:t.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[mt,ht]=re("tabs");var gt=T({name:mt,props:{count:Be(Number),inited:Boolean,animated:Boolean,duration:Be(se),swipeable:Boolean,lazyRender:Boolean,currentIndex:Be(Number)},emits:["change"],setup(t,{emit:a,slots:o}){const i=e(),n=e=>a("change",e),r=()=>{var e;const a=null==(e=o.default)?void 0:e.call(o);return t.animated||t.swipeable?p(ft,{ref:i,loop:!1,class:ht("track"),duration:1e3*+t.duration,touchable:t.swipeable,lazyRender:t.lazyRender,showIndicators:!1,onChange:n},{default:()=>[a]}):a},s=e=>{const a=i.value;a&&a.state.active!==e&&a.swipeTo(e,{immediate:!t.inited})};return l((()=>t.currentIndex),s),q((()=>{s(t.currentIndex)})),Se({swipeRef:i}),()=>p("div",{class:ht("content",{animated:t.animated||t.swipeable})},[r()])}});const[yt,bt]=re("tabs"),wt={type:ue("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:de(0),duration:de(.3),animated:Boolean,ellipsis:xe,swipeable:Boolean,scrollspy:Boolean,offsetTop:de(0),background:String,lazyRender:xe,showHeader:xe,lineWidth:se,lineHeight:se,beforeChange:Function,swipeThreshold:de(5),titleActiveColor:String,titleInactiveColor:String},xt=Symbol(yt);var It=T({name:yt,props:wt,emits:["change","scroll","rendered","clickTab","update:active"],setup(t,{emit:a,slots:o}){let i,n,r,s,u;const d=e(),c=e(),v=e(),f=e(),m=it(),h=ce(d),[g,y]=function(){const t=e([]),a=[];return _((()=>{t.value=[]})),[t,e=>(a[e]||(a[e]=a=>{t.value[e]=a}),a[e])]}(),{children:b,linkChildren:w}=ke(xt),x=j({inited:!1,position:"",lineStyle:{},currentIndex:-1}),I=B((()=>b.length>+t.swipeThreshold||!t.ellipsis||t.shrink)),k=B((()=>({borderColor:t.color,background:t.background}))),S=(e,t)=>{var a;return null!=(a=e.name)?a:t},C=B((()=>{const e=b[x.currentIndex];if(e)return S(e,x.currentIndex)})),$=B((()=>ve(t.offsetTop))),T=B((()=>t.sticky?$.value+i:0)),q=e=>{const a=c.value,l=g.value;if(!(I.value&&a&&l&&l[x.currentIndex]))return;const o=l[x.currentIndex].$el,i=o.offsetLeft-(a.offsetWidth-o.offsetWidth)/2;s&&s(),s=function(e,t,a){let l,o=0;const i=e.scrollLeft,n=0===a?1:Math.round(1e3*a/16);let r=i;return function a(){r+=(t-i)/n,e.scrollLeft=r,++o<n&&(l=ae(a))}(),function(){te(l)}}(a,i,e?0:+t.duration)},O=()=>{const e=x.inited;z((()=>{const a=g.value;if(!a||!a[x.currentIndex]||"line"!==t.type||he(d.value))return;const l=a[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=t,n=l.offsetLeft+l.offsetWidth/2,r={width:ze(o),backgroundColor:t.color,transform:`translateX(${n}px) translateX(-50%)`};if(e&&(r.transitionDuration=`${t.duration}s`),qe(i)){const e=ze(i);r.height=e,r.borderRadius=e}x.lineStyle=r}))},A=(e,l)=>{const o=(e=>{const t=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=t}})(e);if(!qe(o))return;const i=b[o],n=S(i,o),s=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,l||q(),O()),n!==t.active&&(a("update:active",n),s&&a("change",n,i.title)),r&&!t.scrollspy&&Oe(Math.ceil(Ae(d.value)-$.value))},R=(e,t)=>{const a=b.find(((t,a)=>S(t,a)===e)),l=a?b.indexOf(a):0;A(l,t)},U=(e=!1)=>{if(t.scrollspy){const a=b[x.currentIndex].$el;if(a&&h.value){const l=Ae(a,h.value)-T.value;n=!0,u&&u(),u=function(e,t,a,l){let o,i=le(e);const n=i<t,r=0===a?1:Math.round(1e3*a/16),s=(t-i)/r;return function a(){i+=s,(n&&i>t||!n&&i<t)&&(i=t),oe(e,i),n&&i<t||!n&&i>t?o=ae(a):l&&(o=ae(l))}(),function(){te(o)}}(h.value,l,e?0:+t.duration,(()=>{n=!1}))}}},V=(e,l,o)=>{const{title:i,disabled:n}=b[l],r=S(b[l],l);n||(Re(t.beforeChange,{args:[r],done:()=>{A(l),U()}}),Le(e)),a("clickTab",{name:r,title:i,event:o,disabled:n})},E=e=>{r=e.isFixed,a("scroll",e)},M=()=>{if("line"===t.type&&b.length)return p("div",{class:bt("line"),style:x.lineStyle},null)},L=()=>{var e,a,l;const{type:i,border:n,sticky:r}=t,s=[p("div",{ref:r?void 0:v,class:[bt("wrap"),{[Ne]:"line"===i&&n}]},[p("div",{ref:c,role:"tablist",class:bt("nav",[i,{shrink:t.shrink,complete:I.value}]),style:k.value,"aria-orientation":"horizontal"},[null==(e=o["nav-left"])?void 0:e.call(o),b.map((e=>e.renderTitle(V))),M(),null==(a=o["nav-right"])?void 0:a.call(o)])]),null==(l=o["nav-bottom"])?void 0:l.call(o)];return r?p("div",{ref:v},[s]):s},H=()=>{O(),z((()=>{var e,t;q(!0),null==(t=null==(e=f.value)?void 0:e.swipeRef.value)||t.resize()}))};l((()=>[t.color,t.duration,t.lineWidth,t.lineHeight]),O),l(ye,H),l((()=>t.active),(e=>{e!==C.value&&R(e)})),l((()=>b.length),(()=>{x.inited&&(R(t.active),O(),z((()=>{q(!0)})))}));return Se({resize:H,scrollTo:e=>{z((()=>{R(e),U(!0)}))}}),N(O),Ce(O),ne((()=>{R(t.active,!0),z((()=>{x.inited=!0,v.value&&(i=ge(v.value).height),q(!0)}))})),nt(d,O),me("scroll",(()=>{if(t.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:t}=ge(b[e].$el);if(t>T.value)return 0===e?0:e-1}return b.length-1})();A(e)}}),{target:h,passive:!0}),w({id:m,props:t,setLine:O,scrollable:I,onRendered:(e,t)=>a("rendered",e,t),currentName:C,setTitleRefs:y,scrollIntoView:q}),()=>p("div",{ref:d,class:bt([t.type])},[t.showHeader?t.sticky?p(ut,{container:d.value,offsetTop:$.value,onScroll:E},{default:()=>[L()]}):L():null,p(gt,{ref:f,count:b.length,inited:x.inited,animated:t.animated,duration:t.duration,swipeable:t.swipeable,lazyRender:t.lazyRender,currentIndex:x.currentIndex,onChange:A},{default:()=>{var e;return[null==(e=o.default)?void 0:e.call(o)]}})])}});const kt=Symbol(),[St,_t]=re("tab"),Ct=T({name:St,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:se,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:xe},setup(e,{slots:t}){const a=B((()=>{const t={},{type:a,color:l,disabled:o,isActive:i,activeColor:n,inactiveColor:r}=e;l&&"card"===a&&(t.borderColor=l,o||(i?t.backgroundColor=l:t.color=l));const s=i?n:r;return s&&(t.color=s),t})),l=()=>{const a=p("span",{class:_t("text",{ellipsis:!e.scrollable})},[t.title?t.title():e.title]);return e.dot||qe(e.badge)&&""!==e.badge?p(Ue,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[a]}):a};return()=>p("div",{id:e.id,role:"tab",class:[_t([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:a.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[$t,Tt]=re("swipe-item");const jt=we(T({name:$t,setup(e,{slots:t}){let a;const l=j({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=Ve(pt);if(!o)return;const n=B((()=>{const e={},{vertical:t}=o.props;return o.size.value&&(e[t?"height":"width"]=`${o.size.value}px`),l.offset&&(e.transform=`translate${t?"Y":"X"}(${l.offset}px)`),e})),r=B((()=>{const{loop:e,lazyRender:t}=o.props;if(!t||a)return!0;if(!l.mounted)return!1;const n=o.activeIndicator.value,r=o.count.value-1,s=0===n&&e?r:n-1,u=n===r&&e?0:n+1;return a=i.value===n||i.value===s||i.value===u,a}));return q((()=>{z((()=>{l.mounted=!0}))})),Se({setOffset:e=>{l.offset=e}}),()=>{var e;return p("div",{class:Tt(),style:n.value},[r.value?null==(e=t.default)?void 0:e.call(t):null])}}})),[Bt,zt]=re("tab");const qt=we(T({name:Bt,props:pe({},He,{dot:Boolean,name:se,badge:se,title:String,disabled:Boolean,titleClass:Ee,titleStyle:[String,Object],showZeroBadge:xe}),setup(t,{slots:a}){const o=it(),i=e(!1),n=S(),{parent:r,index:s}=Ve(xt);if(!r)return;const u=()=>{var e;return null!=(e=t.name)?e:s.value},d=B((()=>{const e=u()===r.currentName.value;return e&&!i.value&&(i.value=!0,r.props.lazyRender&&z((()=>{r.onRendered(u(),t.title)}))),e})),c=e(""),v=e("");O((()=>{const{titleClass:e,titleStyle:a}=t;c.value=e?lt(e):"",v.value=a&&"string"!=typeof a?function(e){let t="";if(!e||Xe(e))return t;for(const a in e){const l=e[a];(Xe(l)||"number"==typeof l)&&(t+=`${a.startsWith("--")?a:Ge(a)}:${l};`)}return t}(Ke(a)):a}));const f=e(!d.value);return l(d,(e=>{e?f.value=!1:$e((()=>{f.value=!0}))})),l((()=>t.title),(()=>{r.setLine(),r.scrollIntoView()})),A(kt,d),Se({id:o,renderTitle:e=>p(Ct,V({key:o,id:`${r.id}-${s.value}`,ref:r.setTitleRefs(s.value),style:v.value,class:c.value,isActive:d.value,controls:o,scrollable:r.scrollable.value,activeColor:r.props.titleActiveColor,inactiveColor:r.props.titleInactiveColor,onClick:t=>e(n.proxy,s.value,t)},Me(r.props,["type","color","shrink"]),Me(t,["dot","badge","title","disabled","showZeroBadge"])),{title:a.title})}),()=>{var e;const t=`${r.id}-${s.value}`,{animated:l,swipeable:n,scrollspy:u,lazyRender:c}=r.props;if(!a.default&&!l)return;const v=u||d.value;if(l||n)return p(jt,{id:o,role:"tabpanel",class:zt("panel-wrapper",{inactive:f.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":t,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[p("div",{class:zt("panel")},[null==(e=a.default)?void 0:e.call(a)])]}});const m=i.value||u||!c?null==(e=a.default)?void 0:e.call(a):null;return R(p("div",{id:o,role:"tabpanel",class:zt("panel"),tabindex:v?0:-1,"aria-labelledby":t,"data-allow-mismatch":"attribute"},[m]),[[U,v]])}}})),Nt=we(It),Ot={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},At={class:"pc_container",style:{display:"flex",height:"calc(var(--vh) * 100 - 190px)"}},Rt={class:"bg-[#fff]",style:{"border-radius":"10px",width:"40%",height:"calc(var(--vh) * 100 - 190px)"}},Ut={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Vt={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},Et={style:{"margin-top":"15px"}},Mt=["src"],Lt=["src"],Ht={class:"mobile_container"},Wt={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Dt={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},Jt=ee({__name:"index",setup(r){const f=Q("loading.png"),g=Q("copy.png"),y=j({}),b=a(),w={},x=e([]),k=e(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),S=e(null),{locale:_}=E(),C=M();let $=e("a"),T=e("");q((()=>{var e,a;const l=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${l}px`),N();if(["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=b.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(a=b.params)?void 0:a.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),_.value=L(),t.get("userInfo"))ye(),oe();else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=H("current_location_country",1);e&&"中国"!=e?C.push("/login"):window.addLoginDom()}})),l(T,(()=>{T.value&&($.value="b")}));const z=e({appUuid:b.params.appUuid}),N=()=>{W(z.value).then((e=>{sessionStorage.setItem("nodeInfo",JSON.stringify(e[0]))})).catch((e=>{}))},O=()=>{b.params.uuid&&X({appId:b.params.uuid,user:k.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(x.value=e.user_input_form,e.user_input_form.forEach((e=>{const t=Object.keys(e)[0],a=e[t].variable;w[a]={label:e[t].label},y[a]=""})))}))},A=B((()=>!!x.value.length)),U=B((()=>x.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),V=()=>{b.params.uuid&&Z({appId:b.params.uuid,user:k.value.userName}).then((e=>{S.value={...e}}))},ee=e(!1),te=e(!1),ae=e(!1),le=e(!1),oe=async()=>{var e,t;let a=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=k.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:b.params.uuid,userUuid:null==(t=k.value)?void 0:t.openid}];await D.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",a)},ie=()=>{var e,t;if(0!=U.value.length||(a=y,Object.values(a).some((e=>e)))){var a;for(let e in y)if(U.value.includes(e)&&!y[e])return void Y({message:`${w[e].label}为必填项！`,type:"error"});(null==(e=S.value)?void 0:e.mode)&&(["advanced-chat","chat"].includes(null==(t=S.value)?void 0:t.mode)?Y({type:"success",message:"计划中，敬请期待..."}):"completion"==S.value.mode?ce():de())}else Y({message:"请输入您的问题。",type:"error"})},ne=e([]),re=e(!1);let se=!1,ue=!1;const de=async()=>{T.value="",ne.value=[],re.value=!1,se=!1;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run`;0,ee.value=!0,te.value=!0,le.value=!1,await We(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:b.params.uuid,user:k.value.userName,inputs:{...y,outputLanguage:y.outputLanguage?y.outputLanguage:"中文"==G()?"简体中文":G()},files:[],response_mode:"streaming",appUuid:b.params.appUuid}),onmessage(e){var t,a;if(e.data.trim())try{const l=JSON.parse(e.data);if(l.error)throw new Error(l.error);"text_chunk"===l.event&&(ne.value.push(null==(t=null==l?void 0:l.data)?void 0:t.text),re.value||ve()),"workflow_started"===l.event&&(ee.value=!1,ae.value=!0),"workflow_finished"===l.event&&(ne.value.push(null==(a=null==l?void 0:l.data)?void 0:a.outputs.text),re.value||ve(),ae.value=!1,te.value=!1,se=!0,$.value="b")}catch(l){me(l)}},onerror(e){me(e)},openWhenHidden:!0})}catch(e){me(e)}},ce=async()=>{T.value="",ne.value=[],re.value=!1,se=!1;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages`;0,ee.value=!0,te.value=!0,le.value=!1,await We(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:b.params.uuid,user:k.value.userName,inputs:{...y,outputLanguage:G()},files:[],response_mode:"streaming",appUuid:b.params.appUuid}),onmessage(e){if(ee.value=!1,ae.value=!0,e.data.trim())try{const t=JSON.parse(e.data);if(t.error)throw new Error(t.error);"message"===t.event&&(ne.value.push(null==t?void 0:t.answer),re.value||ve()),"message_end"===t.event&&(ae.value=!1,te.value=!1,$.value="b",se=!0)}catch(t){me(t)}},onerror(e){me(e)},openWhenHidden:!0})}catch(e){me(e)}},ve=()=>{if(0===ne.value.length)return re.value=!1,ue=!0,void pe();re.value=!0;const e=ne.value.shift();fe(e).then((()=>{ve()}))},pe=()=>{ue&&se&&(le.value=!0)},fe=e=>new Promise((t=>{let a=0;const l=setInterval((()=>{if(a<e.length){T.value+=e[a++];const t=document.getElementById("typing-area");t.scrollTop=t.scrollHeight}else clearInterval(l),t()}),15)})),me=e=>{ee.value=!1,ae.value=!1,te.value=!1,re.value=!1,Y.error(e.message),T.value=e.message},he=async()=>{try{await navigator.clipboard.writeText(T.value),Y({type:"success",message:"复制成功"})}catch(e){Y(e)}},ge=()=>{for(let e in y)y[e]=""},ye=()=>{if(localStorage.getItem("yudaoToken"))return O(),void V();const e=t.get("userInfo");if(e){const t=JSON.parse(e);try{J({userId:t.userId,userName:t.userName,realName:t.realName,avatar:t.avatar,plaintextUserId:t.plaintextUserId,mobile:t.mobile,email:t.email}).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),O(),V())}))}catch(a){}}};return(e,t)=>{const a=I,l=P("v-md-preview"),r=K;return o(),i("div",Ot,[n("div",At,[s(A)?(o(),i(c,{key:0},[n("div",Rt,[(o(!0),i(c,null,v(s(x),((t,a)=>(o(),i("div",{class:"flex",key:a},[(o(!0),i(c,null,v(t,((t,a)=>(o(),u(Pe,{key:t.variable,type:a,label:e.$t(null==t?void 0:t.label),value:s(y)[null==t?void 0:t.variable],required:null==t?void 0:t.required,placeholder:`${null==t?void 0:t.label}`,max_length:null==t?void 0:t.max_length,options:null==t?void 0:t.options,fileVerify:null==t?void 0:t.allowed_file_types,"onUpdate:value":e=>s(y)[null==t?void 0:t.variable]=e},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Ut,[p(a,{onClick:ge},{default:d((()=>[m("Clear")])),_:1}),p(a,{onClick:ie,loading:s(te),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])]),R((o(),i("div",{style:{width:"60%","margin-left":"15px",padding:"15px","border-radius":"10px"},class:F({"bg-[#fff]":s(T)})},[n("div",Vt,[s(T)?(o(),u(l,{key:0,text:s(T),id:"previewMd"},null,8,["text"])):h("",!0)]),n("div",Et,[s(ae)?(o(),i("img",{key:0,src:s(f),alt:"loading",class:"spinner"},null,8,Mt)):h("",!0),s(le)?(o(),i("img",{key:1,onClick:he,src:s(g),alt:"",style:{width:"20px"},class:"copy"},null,8,Lt)):h("",!0)])],2)),[[r,s(ee)]])],64)):h("",!0)]),n("div",Ht,[p(s(Nt),{active:s($),shrink:"","line-width":"20"},{default:d((()=>[p(s(qt),{title:"输入",name:"a"},{default:d((()=>[(o(!0),i(c,null,v(s(x),((t,a)=>(o(),i("div",{class:"flex",key:a},[(o(!0),i(c,null,v(t,((t,a)=>(o(),u(Pe,{key:t.variable,type:a,label:e.$t(null==t?void 0:t.label),value:s(y)[null==t?void 0:t.variable],required:null==t?void 0:t.required,placeholder:`${null==t?void 0:t.label}`,max_length:null==t?void 0:t.max_length,options:null==t?void 0:t.options,fileVerify:null==t?void 0:t.allowed_file_types,"onUpdate:value":e=>s(y)[null==t?void 0:t.variable]=e},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Wt,[p(a,{onClick:ge},{default:d((()=>[m("Clear")])),_:1}),p(a,{onClick:t[0]||(t[0]=e=>ie()),loading:s(te),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])])),_:1}),p(s(qt),{title:"结果",name:"b"},{default:d((()=>[n("div",Dt,[s(T)?(o(),u(l,{key:0,text:s(T),id:"previewMd"},null,8,["text"])):h("",!0)])])),_:1})])),_:1},8,["active"])])])}}},[["__scopeId","data-v-acc93a30"]]);export{Jt as default};
