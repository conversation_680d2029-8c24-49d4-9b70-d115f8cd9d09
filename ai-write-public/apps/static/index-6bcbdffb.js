/* empty css                  *//* empty css                 */import{s as e}from"./index-b83b7c65.js";import{r as a,aK as l,d as s,e as t,f as u,g as r,q as i,j as n,m as o,k as v,F as m,y as d,U as p,h as c,aM as f,i as x,a3 as y,B as j,E as h,G as g}from"https://static.medsci.cn/ai-write/static/index-f1efeeee.js";import{s as k}from"./index-10f06724.js";/* empty css                   */const w={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},b=["innerHTML"],A={class:"flex justify-center mt-10"},E={__name:"index",setup(E){const M=a(""),S=a([]),V=a([]),q=a(5),z=()=>{if(a=M.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!M.value)return y.warning("请输入英文内容");var a;e({text:M.value}).then((e=>{e&&e&&e.data&&(S.value=e.data,S.value=k(S.value),q.value=5,C())}))},C=()=>{let e=[];5==q.value?(e=[0,5],q.value=3):3==q.value?(e=[5,8],q.value=2):2==q.value&&(e=[8,10],q.value=5),V.value=JSON.parse(JSON.stringify(S.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,k=h,E=g,S=l("copy");return s(),t("div",w,[u(y,{modelValue:r(M),"onUpdate:modelValue":a[0]||(a[0]=e=>i(M)?M.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(k,{type:"primary",onClick:z},{default:o((()=>a[1]||(a[1]=[v("改 写")]))),_:1})]),r(V)&&r(V).length?(s(),t("div",_,[(s(!0),t(m,null,d(r(V),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,b),p((s(),c(E,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(r(f))])),_:2},1024)),[[S,e.text]])])))),128)),n("div",A,[u(k,{type:"primary",link:"",onClick:C},{default:o((()=>a[2]||(a[2]=[v("换一换")]))),_:1})])])):x("",!0)])}}};export{E as default};
