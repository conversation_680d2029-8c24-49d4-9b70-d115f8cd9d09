import{x as e,au as a,X as s,ah as o,ai as t,av as n,aw as l,c as r,d as i,i as c,t as d,h as u,F as m,y as h,ac as g,$ as p,ae as v,af as w,j as f}from"./index-d9e5ab42.js";import{_ as I}from"./_plugin-vue_export-helper-1b428a4d.js";const y={data:()=>({avatar:"",userInfo:null,isIncludeTool:!1,selectedLanguage:"",langs:[],hrefUrl:""}),watch:{userInfoCookie(e,a){}},computed:{baseUrl:()=>"https://www.medsci.cn/",userInfoCookie:()=>e.get("userInfo")},methods:{changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},getCookieUserInfo:()=>e.get("userInfo"),showMenu(){this.$refs.menu.style.display="block"},hideMenu(){this.$refs.menu.style.display="none"},showMenu1(){this.$refs.menu1.style.display="block"},hideMenu1(){this.$refs.menu1.style.display="none"},toggle(e){const a=this.$route.path,s=this.$route.params.lang;if(s){const o=a.replace(`/${s}`,"");this.$router.push(`/${e}${o}`)}else this.$router.push(`/${e}${a}`);window.localStorage.setItem("ai_apps_lang",e),this.selectedLanguage=e,this.$i18n.locale=e,this.$emit("getAppLang",e),"zh-CN"==localStorage.getItem("ai_apps_lang")?this.$emit("isZHChange",!0):this.$emit("isZHChange",!1),this.$refs.menu.style.display="none"},async logout(){e.remove("userInfo",{domain:".medon.com.cn"}),e.remove("userInfo",{domain:".medsci.cn"}),e.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("conversation"),localStorage.removeItem("writeContent");let s=localStorage.getItem("socialType");s&&35==s||s&&36==s?a().then((()=>{localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),location.reload()})).catch((e=>{})):(localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),window.location.origin.includes("medsci.cn")?window.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.location.href:window.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.location.href)},async loginAccount(){let e=await this.getLocationData();e&&"中国"!=e?this.$router.push("/login"):window.addLoginDom()},medsciSearch(){let e=document.getElementById("medsciSearchKeyword").value;e?window.open(`https://www.medsci.cn/search?q=${e}`,"_blank"):window.open("https://search.medsci.cn/","_blank")},async getLocationData(){let e=s("current_location_country",1);return e||await o().then((e=>{t("current_location_country",e)})),e=s("current_location_country",1),e},getAppLangsData(){n().then((e=>{e.map((e=>{e.status||this.langs.push({name:e.value,value:e.remark})}))}))}},mounted(){var a,s;this.hrefUrl="https://ai.medon.com.cn",this.getAppLangsData(),this.userInfo=e.get("userInfo")?JSON.parse(e.get("userInfo")):null,this.isIncludeTool=window.location.href.includes("/tool"),setTimeout((()=>{this.selectedLanguage=l()}),0),this.avatar=(null==(a=this.userInfo)?void 0:a.avatar)?null==(s=this.userInfo)?void 0:s.avatar:"https://img.medsci.cn/web/img/user_icon.png"}},k=e=>(v("data-v-********"),e=e(),w(),e),_={class:"header ms-header-media"},$={class:"ms-header"},S={class:"wrapper"},M={class:"main-menu-placeholder wrapper clearfix",style:{height:"56px",display:"block !important"}},b={class:"ms-header-img"},C=["href"],T=[k((()=>c("img",{src:"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:""},null,-1)))],x={id:"main-menu",class:"ms-header-nav"},L={class:"header-top header-user",id:"user-info-header"},U={key:0,class:"change_lang"},A={class:"current_lang"},j={class:"ms-link"},D={class:"ms-dropdown-menu",ref:"menu"},N={class:"new-header-avator-pop",id:"new-header-avator"},q={class:"new-header-bottom",style:{padding:"0"}},E={class:"langUl"},H=["onClick"],Z={class:"index-user-img_right"},z=k((()=>c("li",null,null,-1))),B={href:"#"},F={class:"img-area"},G=["src"],J={class:"ms-dropdown-menu",ref:"menu1"},K={class:"new-header-avator-pop",id:"new-header-avator"},O={class:"new-header-top"},Q={class:"new-header-info"},R=["src"],X={class:"new-header-name"};const P=I(y,[["render",function(e,a,s,o,t,n){var l,v,w,f,I;return r(),i("div",_,[c("div",$,[c("div",S,[c("div",M,[c("div",b,[c("a",{href:t.hrefUrl},T,8,C)]),c("div",x,[c("div",L,[c("ul",null,[c("li",{class:"index-user-img index-user-img_left",onMouseover:a[0]||(a[0]=(...e)=>n.showMenu&&n.showMenu(...e)),onMouseout:a[1]||(a[1]=(...e)=>n.hideMenu&&n.hideMenu(...e)),onClick:a[2]||(a[2]=(...e)=>n.showMenu&&n.showMenu(...e))},[t.isIncludeTool?u("",!0):(r(),i("div",U,[c("span",A,d(null==(l=t.langs.filter((e=>e.value==t.selectedLanguage))[0])?void 0:l.name),1),c("span",j,d(e.$t("market.switch")),1)])),c("div",D,[c("div",N,[c("div",q,[c("div",E,[(r(!0),i(m,null,h(t.langs,(e=>(r(),i("p",{key:e,onClick:g((a=>n.toggle(e.value)),["stop"]),class:p({langItemSelected:e.value===t.selectedLanguage})},d(e.name),11,H)))),128))])])])],512)],32),(null==(v=t.userInfo)?void 0:v.userId)?(r(),i("li",{key:1,class:"index-user-img",onMouseover:a[7]||(a[7]=(...e)=>n.showMenu1&&n.showMenu1(...e)),onMouseout:a[8]||(a[8]=(...e)=>n.hideMenu1&&n.hideMenu1(...e))},[c("a",B,[c("div",F,[c("img",{src:t.avatar,onError:a[4]||(a[4]=(...e)=>n.changeImg&&n.changeImg(...e)),alt:""},null,40,G)])]),c("div",J,[c("div",K,[c("a",{class:"new-header-exit ms-statis","ms-statis":"logout",href:"#",onClick:a[5]||(a[5]=e=>n.logout())},d(e.$t("market.logout")),1),c("div",O,[c("div",Q,[c("img",{class:"new-header-avatar",src:t.avatar,onError:a[6]||(a[6]=(...e)=>n.changeImg&&n.changeImg(...e)),alt:""},null,40,R),c("div",X,[c("span",null,d((null==(w=t.userInfo)?void 0:w.realName)?null==(f=t.userInfo)?void 0:f.realName:null==(I=t.userInfo)?void 0:I.userName),1)])])])])],512)],32)):(r(),i(m,{key:0},[c("li",Z,[c("a",{href:"javascript: void(0)",class:"ms-link",onClick:a[3]||(a[3]=(...e)=>n.loginAccount&&n.loginAccount(...e))},d(e.$t("market.login")),1)]),z],64))])])])])])])])}],["__scopeId","data-v-********"]]),V={name:"AssistantComponent",data:()=>({}),methods:{}},W=e=>(v("data-v-39a9c511"),e=e(),w(),e),Y={class:"assistant-container"},ee=[W((()=>c("div",{class:"assistant-icon"},[c("img",{src:"/apps/static/kefu-ae1166e2.png",class:"fas fa-user-astronaut",alt:"客服"})],-1))),W((()=>c("div",{class:"qr-code"},[c("img",{src:"/apps/static/qrcode-4bdb4a15.png",alt:"QR Code"}),f(" 扫码添加小助手 ")],-1)))];const ae=I(V,[["render",function(e,a,s,o,t,n){return r(),i("div",Y,ee)}],["__scopeId","data-v-39a9c511"]]);export{P as _,ae as c};
