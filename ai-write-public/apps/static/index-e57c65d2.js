import{_ as e,a as l}from"./Editor-d8350656.js";/* empty css                  *//* empty css                 */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                    *//* empty css                  */import{a}from"./index-c4e7b377.js";import{s as t}from"./index-5d3c44df.js";/* empty css                   */import{a as s,r as o,O as n,o as r,d as i,e as d,j as u,F as p,z as m,a0 as c,g as v,i as f,t as b,f as x,q as g,m as y,k as h,h as k,a4 as V,C as j,E as w,af as C,aJ as _,aK as S,aL as T,ag as z,ah as M}from"./index-2502211c.js";/* empty css                  *//* empty css                */const U=e=>(z("data-v-1e15cf5a"),e=e(),M(),e),N={class:"h-full flex overflow-auto"},H={class:"flex flex-col w-[160px] mr-4"},L=["onClick"],$={key:0,class:"w-[4px] h-full"},A={class:"flex-1"},q={class:"flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2"},E={class:"flex items-center my-8 overflow-hidden"},I={class:"flex items-center mr-4"},K={class:"flex items-center mr-2"},R=U((()=>u("span",{class:"mr-2"},"影响因子：",-1))),Y={class:"flex items-center"},D=U((()=>u("div",{class:"w-[60px]"},"年份范围",-1))),F={class:"relative flex-1"},G={class:"text-[#419eff] absolute font-bold -top-1.5 left-[30%]"},J={class:"mr-2"},O=["innerHTML","onClick"],P=["innerHTML"],W=s({__name:"index",setup(s){const z=o("类风湿关节炎铁死亡特征基因图的构建"),M=o(""),U=o(1),W=o(!0),B=o([]),Q=o([1990,2023]),X=o({}),Z=n({pageNo:1,pageSize:20}),ee=o([{label:"Title",value:"title"},{label:"Keywords",value:"keyword"},{label:"Abstract",value:"abstract"},{label:"Introduction",value:"introduction"},{label:"Methods",value:"methods"},{label:"Results",value:"results"},{label:"Discussion",value:"discussion"},{label:"Acknowledge",value:"acknowleg"},{label:"全库检索(CNS)",value:"all"}]),le=o("title"),ae=e=>{if(!z.value)return V.warning("请输入关键词或短句");1==e&&(Z.pageNo=1);let l=B.value.map((e=>({field:"effect",opt:2,vals:[e],val:"",synonymsWordVos:[]})));a(le.value,{key:z.value,page:Z.pageNo-1,size:Z.pageSize,allMySentence:0,allMyGroupSentence:0,synonymsHistory:0,mulSearchConditions:l,beginYear:Q.value[0],endYear:Q.value[1],mySentenceRange:0,sorts:[]}).then((e=>{e&&e.data&&(X.value=e.data,X.value.content=t(X.value.content))}))},te=()=>{U.value++},se=()=>{ae()};return r((()=>{ae()})),(a,t)=>{const s=j,o=w,n=e,r=C,V=_,oe=S,ne=T,re=l;return i(),d("div",N,[u("div",H,[(i(!0),d(p,null,m(v(ee),((e,l)=>(i(),d("div",{class:c(["nav-item flex items-center font-bold text-[#2d3858] cursor-pointer border border-solid border-top-0 border-left-0 border-right-0 border-gray-200 h-[46px]",v(le)==e.value?"bg-[#7e90b8]":"bg-[#f8f8f8]"]),key:l,onClick:l=>(e=>{le.value=e.value,z.value="",X.value={},B.value=[]})(e)},[v(le)!=e.value?(i(),d("div",$)):f("",!0),u("div",{class:c(["pl-10 h-full flex items-center",v(le)==e.value?"border border-solid border-left-4 border-[#2b3858] border-top-0 border-bottom-0 border-right-0":""])},b(e.label),3)],10,L)))),128))]),u("div",A,[u("div",q,[x(s,{class:"h-full !text-[24px]",modelValue:v(z),"onUpdate:modelValue":t[0]||(t[0]=e=>g(z)?z.value=e:null),placeholder:"这里输入关键词或短句，中英文均可",clearable:""},null,8,["modelValue"]),x(o,{type:"primary",onClick:t[1]||(t[1]=e=>ae(1))},{default:y((()=>[h("查 询")])),_:1})]),(i(),k(n,{class:"h-[380px] mb-4",modelValue:v(M),"onUpdate:modelValue":t[2]||(t[2]=e=>g(M)?M.value=e:null),onClear:te,key:v(U)},null,8,["modelValue"])),u("div",E,[u("div",I,[u("div",{class:c(["mr-2",v(W)?"text-[#409eff]":""])},"翻译",2),x(r,{modelValue:v(W),"onUpdate:modelValue":t[3]||(t[3]=e=>g(W)?W.value=e:null)},null,8,["modelValue"])]),u("div",K,[R,x(oe,{modelValue:v(B),"onUpdate:modelValue":t[4]||(t[4]=e=>g(B)?B.value=e:null)},{default:y((()=>[x(V,{label:"3"},{default:y((()=>[h(b("<3分"))])),_:1}),x(V,{label:"5"},{default:y((()=>[h("3-10分")])),_:1}),x(V,{label:"10"},{default:y((()=>[h(b(">10分"))])),_:1})])),_:1},8,["modelValue"])]),u("div",Y,[D,u("div",F,[x(ne,{class:"ml-6 !w-[132px]",modelValue:v(Q),"onUpdate:modelValue":t[5]||(t[5]=e=>g(Q)?Q.value=e:null),range:"",max:2023,min:1990,onChange:se},null,8,["modelValue"]),u("span",G,b(`${v(Q)[0]}-${v(Q)[1]}`),1)])])]),u("div",null,[(i(!0),d(p,null,m(v(X).content,((e,l)=>(i(),d("div",{class:"flex mb-8",key:l},[u("div",J,b(l+1)+".",1),u("div",null,[u("div",{class:"text-[18px] text-[#5e5e5e] cursor-pointer html-value",innerHTML:e.text,onClick:a=>((e,l)=>{let a=`${e}.${l}`.replace(/\n/g,"");M.value+=`<p>${a}</p>`})(l+1,e.text)},null,8,O),v(W)?(i(),d("div",{key:0,class:"text-[16px] text-[#0a76f5] mt-4",innerHTML:e.translateText},null,8,P)):f("",!0)])])))),128))]),v(X)&&v(X).eleTotal?(i(),k(re,{key:0,class:"pb-10",total:v(X).eleTotal,page:v(Z).pageNo,"onUpdate:page":t[6]||(t[6]=e=>v(Z).pageNo=e),limit:v(Z).pageSize,"onUpdate:limit":t[7]||(t[7]=e=>v(Z).pageSize=e),onPagination:ae},null,8,["total","page","limit"])):f("",!0)])])}}},[["__scopeId","data-v-1e15cf5a"]]);export{W as default};
