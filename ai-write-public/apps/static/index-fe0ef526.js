/* empty css                  *//* empty css                   */import{r as e,y as a,u as t,w as l,d as o,e as i,j as n,t as r,g as s,h as u,m as d,F as c,z as v,f as p,A as f,k as m,i as h,B as g,C as y,D as b,G as w,H as x,E as k,I,J as _,K as S,L as C,M as $,N as T,O as j,P as B,n as z,o as N,Q as q,R as A,S as O,T as R,U as E,V as M,a as V,b as U,W as L,X as H,Y as W,Z as D,$ as J,c as P,a0 as F,a1 as X,a2 as Z,a3 as Y,a4 as G,a5 as K,a6 as Q}from"./index-2502211c.js";import{g as ee}from"./index-5d3c44df.js";/* empty css                *//* empty css                 *//* empty css                  *//* empty css                  */import{c as ae,r as te,g as le,s as oe,i as ie,o as ne,a as re,n as se,m as ue,b as de,u as ce,d as ve,e as pe,f as fe,h as me,j as he,k as ge,w as ye,l as be,p as we,t as xe,q as ke,v as Ie,x as _e,y as Se,z as Ce,A as $e,B as Te,C as je,D as Be,E as ze,F as Ne,G as qe,H as Ae,I as Oe,J as Re,K as Ee,L as Me,M as Ve,N as Ue}from"./use-touch-e98ac5a7.js";import{r as Le,a as He,f as We}from"./use-route-4a5e1b98.js";const De={class:"p-3 flex-1 rounded-md"},Je={class:"text-[14px] font-bold mb-2 text-gray-600"},Pe={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(_,{expose:S,emit:C}){const $=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),T=t(),j=e([]),B=e({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),z=e(""),N=_,q=N.type,A=N.fileVerify,O=N.label,R=N.required,E=N.max_length,M=N.options;"file"==q&&(z.value=null),"file-list"==q&&(z.value=[]);const V={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},U=()=>{let e="";return A.forEach(((a,t)=>{t<A.length-1?e+=V[a].join(",")+",":e+=V[a].join(",")})),e},L=C,H=(e,a,t)=>{},W=()=>{z.value=""},D=async e=>{const{file:a,onSuccess:t,onError:l}=e,o=new FormData;o.append("file",a),o.append("appId",T.params.uuid),o.append("user",$.value.userName);try{const e=await g(o);"file-list"==q?z.value.push({type:B.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):z.value={type:B.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},t(e,a)}catch(i){l(i)}return!1};M&&M.length>0&&(z.value=M[0]);return S({updateMessage:()=>{M&&M.length>0?z.value=M[0]:"file"==q?(z.value=null,j.value=[]):"file-list"==q?(z.value=[],j.value=[]):z.value=""}}),l(z,(e=>{L("update:value",e)}),{immediate:!0,deep:!0}),(e,a)=>{const t=y,l=b,g=w,_=x,S=k,C=I;return o(),i("div",De,[n("div",Je,r(s(O)),1),"paragraph"===s(q)||"text-input"===s(q)?(o(),u(t,{key:0,modelValue:z.value,"onUpdate:modelValue":a[0]||(a[0]=e=>z.value=e),type:"paragraph"===s(q)?"textarea":"text",rows:5,required:s(R),placeholder:`${s(O)}`,"show-word-limit":"",resize:"none",maxlength:s(E)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===s(q)?(o(),u(t,{key:1,modelValue:z.value,"onUpdate:modelValue":a[1]||(a[1]=e=>z.value=e),modelModifiers:{number:!0},type:"number",required:s(R),placeholder:`${s(O)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===s(q)?(o(),u(g,{key:2,modelValue:z.value,"onUpdate:modelValue":a[2]||(a[2]=e=>z.value=e),required:s(R),placeholder:`${s(O)}`},{default:d((()=>[(o(!0),i(c,null,v(s(M),(e=>(o(),u(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===s(q)||"file-list"===s(q)?(o(),u(C,{key:3,"file-list":j.value,"onUpdate:fileList":a[3]||(a[3]=e=>j.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":W,"before-remove":e.beforeRemove,limit:s(E),accept:U(),"auto-upload":!0,"on-Success":H,"http-request":D,"on-exceed":e.handleExceed},{default:d((()=>[p(S,{disabled:j.value.length==s(E)},{default:d((()=>[p(_,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:d((()=>[p(s(f))])),_:1}),m("从本地上传")])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):h("",!0)])}}},Fe=Array.isArray,Xe=e=>"string"==typeof e,Ze=e=>null!==e&&"object"==typeof e,Ye=/\B([A-Z])/g,Ge=(e=>{const a=Object.create(null);return t=>a[t]||(a[t]=e(t))})((e=>e.replace(Ye,"-$1").toLowerCase()));function Ke(e){if(Fe(e)){const a={};for(let t=0;t<e.length;t++){const l=e[t],o=Xe(l)?ta(l):Ke(l);if(o)for(const e in o)a[e]=o[e]}return a}if(Xe(e)||Ze(e))return e}const Qe=/;(?![^(]*\))/g,ea=/:([^]+)/,aa=/\/\*[^]*?\*\//g;function ta(e){const a={};return e.replace(aa,"").split(Qe).forEach((e=>{if(e){const t=e.split(ea);t.length>1&&(a[t[0].trim()]=t[1].trim())}})),a}function la(e){let a="";if(Xe(e))a=e;else if(Fe(e))for(let t=0;t<e.length;t++){const l=la(e[t]);l&&(a+=l+" ")}else if(Ze(e))for(const t in e)e[t]&&(a+=t+" ");return a.trim()}let oa=0;function ia(){const e=_(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++oa}`}function na(e,a){if(!ie||!window.IntersectionObserver)return;const t=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&t.unobserve(e.value)};C(l),$(l),ne((()=>{e.value&&t.observe(e.value)}))}const[ra,sa]=re("sticky");const ua=we(T({name:ra,props:{zIndex:se,position:ue("top"),container:Object,offsetTop:de(0),offsetBottom:de(0)},emits:["scroll","change"],setup(a,{emit:t,slots:o}){const i=e(),n=ce(i),r=j({fixed:!1,width:0,height:0,transform:0}),s=e(!1),u=B((()=>ve("top"===a.position?a.offsetTop:a.offsetBottom))),d=B((()=>{if(s.value)return;const{fixed:e,height:a,width:t}=r;return e?{width:`${t}px`,height:`${a}px`}:void 0})),c=B((()=>{if(!r.fixed||s.value)return;const e=pe(fe(a.zIndex),{width:`${r.width}px`,height:`${r.height}px`,[a.position]:`${u.value}px`});return r.transform&&(e.transform=`translate3d(0, ${r.transform}px, 0)`),e})),v=()=>{if(!i.value||he(i))return;const{container:e,position:l}=a,o=ge(i),n=le(window);if(r.width=o.width,r.height=o.height,"top"===l)if(e){const a=ge(e),t=a.bottom-u.value-r.height;r.fixed=u.value>o.top&&a.bottom>0,r.transform=t<0?t:0}else r.fixed=u.value>o.top;else{const{clientHeight:a}=document.documentElement;if(e){const t=ge(e),l=a-t.top-u.value-r.height;r.fixed=a-u.value<o.bottom&&a>t.top,r.transform=l<0?-l:0}else r.fixed=a-u.value<o.bottom}(e=>{t("scroll",{scrollTop:e,isFixed:r.fixed})})(n)};return l((()=>r.fixed),(e=>t("change",e))),me("scroll",v,{target:n,passive:!0}),na(i,v),l([ye,be],(()=>{i.value&&!he(i)&&r.fixed&&(s.value=!0,z((()=>{const e=ge(i);r.width=e.width,r.height=e.height,s.value=!1})))})),()=>{var e;return p("div",{ref:i,style:d.value},[p("div",{class:sa({fixed:r.fixed&&!s.value}),style:c.value},[null==(e=o.default)?void 0:e.call(o)])])}}})),[da,ca]=re("swipe"),va={loop:xe,width:se,height:se,vertical:Boolean,autoplay:de(0),duration:de(500),touchable:xe,lazyRender:Boolean,initialSwipe:de(0),indicatorColor:String,showIndicators:xe,stopPropagation:xe},pa=Symbol(da);const fa=we(T({name:da,props:va,emits:["change","dragStart","dragEnd"],setup(a,{emit:t,slots:o}){const i=e(),n=e(),r=j({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let s=!1;const u=ke(),{children:d,linkChildren:c}=Ie(pa),v=B((()=>d.length)),f=B((()=>r[a.vertical?"height":"width"])),m=B((()=>a.vertical?u.deltaY.value:u.deltaX.value)),h=B((()=>{if(r.rect){return(a.vertical?r.rect.height:r.rect.width)-f.value*v.value}return 0})),g=B((()=>f.value?Math.ceil(Math.abs(h.value)/f.value):v.value)),y=B((()=>v.value*f.value)),b=B((()=>(r.active+v.value)%v.value)),w=B((()=>{const e=a.vertical?"vertical":"horizontal";return u.direction.value===e})),x=B((()=>{const e={transitionDuration:`${r.swiping?0:a.duration}ms`,transform:`translate${a.vertical?"Y":"X"}(${+r.offset.toFixed(2)}px)`};if(f.value){const t=a.vertical?"height":"width",l=a.vertical?"width":"height";e[t]=`${y.value}px`,e[l]=a[l]?`${a[l]}px`:""}return e})),k=(e,t=0)=>{let l=e*f.value;a.loop||(l=Math.min(l,-h.value));let o=t-l;return a.loop||(o=je(o,h.value,0)),o},I=({pace:e=0,offset:l=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=r,n=(e=>{const{active:t}=r;return e?a.loop?je(t+e,-1,v.value):je(t+e,0,g.value):t})(e),s=k(n,l);if(a.loop){if(d[0]&&s!==h.value){const e=s<h.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==s){const e=s>0;d[v.value-1].setOffset(e?-y.value:0)}}r.active=n,r.offset=s,o&&n!==i&&t("change",b.value)},_=()=>{r.swiping=!0,r.active<=-1?I({pace:v.value}):r.active>=v.value&&I({pace:-v.value})},S=()=>{_(),u.reset(),$e((()=>{r.swiping=!1,I({pace:1,emitChange:!0})}))};let T;const A=()=>clearTimeout(T),O=()=>{A(),+a.autoplay>0&&v.value>1&&(T=setTimeout((()=>{S(),O()}),+a.autoplay))},R=(e=+a.initialSwipe)=>{if(!i.value)return;const t=()=>{var t,l;if(!he(i)){const e={width:i.value.offsetWidth,height:i.value.offsetHeight};r.rect=e,r.width=+(null!=(t=a.width)?t:e.width),r.height=+(null!=(l=a.height)?l:e.height)}v.value&&-1===(e=Math.min(v.value-1,e))&&(e=v.value-1),r.active=e,r.swiping=!0,r.offset=k(e),d.forEach((e=>{e.setOffset(0)})),O()};he(i)?z().then(t):t()},E=()=>R(r.active);let M;const V=e=>{!a.touchable||e.touches.length>1||(u.start(e),s=!1,M=Date.now(),A(),_())},U=()=>{if(!a.touchable||!r.swiping)return;const e=Date.now()-M,l=m.value/e;if((Math.abs(l)>.25||Math.abs(m.value)>f.value/2)&&w.value){const e=a.vertical?u.offsetY.value:u.offsetX.value;let t=0;t=a.loop?e>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/f.value),I({pace:t,emitChange:!0})}else m.value&&I({pace:0});s=!1,r.swiping=!1,t("dragEnd",{index:b.value}),O()},L=(e,t)=>{const l=t===b.value,o=l?{backgroundColor:a.indicatorColor}:void 0;return p("i",{style:o,class:ca("indicator",{active:l})},null)};return _e({prev:()=>{_(),u.reset(),$e((()=>{r.swiping=!1,I({pace:-1,emitChange:!0})}))},next:S,state:r,resize:E,swipeTo:(e,t={})=>{_(),u.reset(),$e((()=>{let l;l=a.loop&&e===v.value?0===r.active?0:e:e%v.value,t.immediate?$e((()=>{r.swiping=!1})):r.swiping=!1,I({pace:l-r.active,emitChange:!0})}))}}),c({size:f,props:a,count:v,activeIndicator:b}),l((()=>a.initialSwipe),(e=>R(+e))),l(v,(()=>R(r.active))),l((()=>a.autoplay),O),l([ye,be,()=>a.width,()=>a.height],E),l(Se(),(e=>{"visible"===e?O():A()})),N(R),q((()=>R(r.active))),Ce((()=>R(r.active))),C(A),$(A),me("touchmove",(e=>{if(a.touchable&&r.swiping&&(u.move(e),w.value)){!a.loop&&(0===r.active&&m.value>0||r.active===v.value-1&&m.value<0)||(Te(e,a.stopPropagation),I({offset:m.value}),s||(t("dragStart",{index:b.value}),s=!0))}}),{target:n}),()=>{var e;return p("div",{ref:i,class:ca()},[p("div",{ref:n,style:x.value,class:ca("track",{vertical:a.vertical}),onTouchstartPassive:V,onTouchend:U,onTouchcancel:U},[null==(e=o.default)?void 0:e.call(o)]),o.indicator?o.indicator({active:b.value,total:v.value}):a.showIndicators&&v.value>1?p("div",{class:ca("indicators",{vertical:a.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[ma,ha]=re("tabs");var ga=T({name:ma,props:{count:Be(Number),inited:Boolean,animated:Boolean,duration:Be(se),swipeable:Boolean,lazyRender:Boolean,currentIndex:Be(Number)},emits:["change"],setup(a,{emit:t,slots:o}){const i=e(),n=e=>t("change",e),r=()=>{var e;const t=null==(e=o.default)?void 0:e.call(o);return a.animated||a.swipeable?p(fa,{ref:i,loop:!1,class:ha("track"),duration:1e3*+a.duration,touchable:a.swipeable,lazyRender:a.lazyRender,showIndicators:!1,onChange:n},{default:()=>[t]}):t},s=e=>{const t=i.value;t&&t.state.active!==e&&t.swipeTo(e,{immediate:!a.inited})};return l((()=>a.currentIndex),s),N((()=>{s(a.currentIndex)})),_e({swipeRef:i}),()=>p("div",{class:ha("content",{animated:a.animated||a.swipeable})},[r()])}});const[ya,ba]=re("tabs"),wa={type:ue("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:de(0),duration:de(.3),animated:Boolean,ellipsis:xe,swipeable:Boolean,scrollspy:Boolean,offsetTop:de(0),background:String,lazyRender:xe,showHeader:xe,lineWidth:se,lineHeight:se,beforeChange:Function,swipeThreshold:de(5),titleActiveColor:String,titleInactiveColor:String},xa=Symbol(ya);var ka=T({name:ya,props:wa,emits:["change","scroll","rendered","clickTab","update:active"],setup(a,{emit:t,slots:o}){let i,n,r,s,u;const d=e(),c=e(),v=e(),f=e(),m=ia(),h=ce(d),[g,y]=function(){const a=e([]),t=[];return S((()=>{a.value=[]})),[a,e=>(t[e]||(t[e]=t=>{a.value[e]=t}),t[e])]}(),{children:b,linkChildren:w}=Ie(xa),x=j({inited:!1,position:"",lineStyle:{},currentIndex:-1}),k=B((()=>b.length>+a.swipeThreshold||!a.ellipsis||a.shrink)),I=B((()=>({borderColor:a.color,background:a.background}))),_=(e,a)=>{var t;return null!=(t=e.name)?t:a},C=B((()=>{const e=b[x.currentIndex];if(e)return _(e,x.currentIndex)})),$=B((()=>ve(a.offsetTop))),T=B((()=>a.sticky?$.value+i:0)),N=e=>{const t=c.value,l=g.value;if(!(k.value&&t&&l&&l[x.currentIndex]))return;const o=l[x.currentIndex].$el,i=o.offsetLeft-(t.offsetWidth-o.offsetWidth)/2;s&&s(),s=function(e,a,t){let l,o=0;const i=e.scrollLeft,n=0===t?1:Math.round(1e3*t/16);let r=i;return function t(){r+=(a-i)/n,e.scrollLeft=r,++o<n&&(l=te(t))}(),function(){ae(l)}}(t,i,e?0:+a.duration)},A=()=>{const e=x.inited;z((()=>{const t=g.value;if(!t||!t[x.currentIndex]||"line"!==a.type||he(d.value))return;const l=t[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=a,n=l.offsetLeft+l.offsetWidth/2,r={width:ze(o),backgroundColor:a.color,transform:`translateX(${n}px) translateX(-50%)`};if(e&&(r.transitionDuration=`${a.duration}s`),Ne(i)){const e=ze(i);r.height=e,r.borderRadius=e}x.lineStyle=r}))},O=(e,l)=>{const o=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(e);if(!Ne(o))return;const i=b[o],n=_(i,o),s=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,l||N(),A()),n!==a.active&&(t("update:active",n),s&&t("change",n,i.title)),r&&!a.scrollspy&&Ae(Math.ceil(Oe(d.value)-$.value))},R=(e,a)=>{const t=b.find(((a,t)=>_(a,t)===e)),l=t?b.indexOf(t):0;O(l,a)},E=(e=!1)=>{if(a.scrollspy){const t=b[x.currentIndex].$el;if(t&&h.value){const l=Oe(t,h.value)-T.value;n=!0,u&&u(),u=function(e,a,t,l){let o,i=le(e);const n=i<a,r=0===t?1:Math.round(1e3*t/16),s=(a-i)/r;return function t(){i+=s,(n&&i>a||!n&&i<a)&&(i=a),oe(e,i),n&&i<a||!n&&i>a?o=te(t):l&&(o=te(l))}(),function(){ae(o)}}(h.value,l,e?0:+a.duration,(()=>{n=!1}))}}},M=(e,l,o)=>{const{title:i,disabled:n}=b[l],r=_(b[l],l);n||(Re(a.beforeChange,{args:[r],done:()=>{O(l),E()}}),Le(e)),t("clickTab",{name:r,title:i,event:o,disabled:n})},V=e=>{r=e.isFixed,t("scroll",e)},U=()=>{if("line"===a.type&&b.length)return p("div",{class:ba("line"),style:x.lineStyle},null)},L=()=>{var e,t,l;const{type:i,border:n,sticky:r}=a,s=[p("div",{ref:r?void 0:v,class:[ba("wrap"),{[qe]:"line"===i&&n}]},[p("div",{ref:c,role:"tablist",class:ba("nav",[i,{shrink:a.shrink,complete:k.value}]),style:I.value,"aria-orientation":"horizontal"},[null==(e=o["nav-left"])?void 0:e.call(o),b.map((e=>e.renderTitle(M))),U(),null==(t=o["nav-right"])?void 0:t.call(o)])]),null==(l=o["nav-bottom"])?void 0:l.call(o)];return r?p("div",{ref:v},[s]):s},H=()=>{A(),z((()=>{var e,a;N(!0),null==(a=null==(e=f.value)?void 0:e.swipeRef.value)||a.resize()}))};l((()=>[a.color,a.duration,a.lineWidth,a.lineHeight]),A),l(ye,H),l((()=>a.active),(e=>{e!==C.value&&R(e)})),l((()=>b.length),(()=>{x.inited&&(R(a.active),A(),z((()=>{N(!0)})))}));return _e({resize:H,scrollTo:e=>{z((()=>{R(e),E(!0)}))}}),q(A),Ce(A),ne((()=>{R(a.active,!0),z((()=>{x.inited=!0,v.value&&(i=ge(v.value).height),N(!0)}))})),na(d,A),me("scroll",(()=>{if(a.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=ge(b[e].$el);if(a>T.value)return 0===e?0:e-1}return b.length-1})();O(e)}}),{target:h,passive:!0}),w({id:m,props:a,setLine:A,scrollable:k,onRendered:(e,a)=>t("rendered",e,a),currentName:C,setTitleRefs:y,scrollIntoView:N}),()=>p("div",{ref:d,class:ba([a.type])},[a.showHeader?a.sticky?p(ua,{container:d.value,offsetTop:$.value,onScroll:V},{default:()=>[L()]}):L():null,p(ga,{ref:f,count:b.length,inited:x.inited,animated:a.animated,duration:a.duration,swipeable:a.swipeable,lazyRender:a.lazyRender,currentIndex:x.currentIndex,onChange:O},{default:()=>{var e;return[null==(e=o.default)?void 0:e.call(o)]}})])}});const Ia=Symbol(),[_a,Sa]=re("tab"),Ca=T({name:_a,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:se,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:xe},setup(e,{slots:a}){const t=B((()=>{const a={},{type:t,color:l,disabled:o,isActive:i,activeColor:n,inactiveColor:r}=e;l&&"card"===t&&(a.borderColor=l,o||(i?a.backgroundColor=l:a.color=l));const s=i?n:r;return s&&(a.color=s),a})),l=()=>{const t=p("span",{class:Sa("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||Ne(e.badge)&&""!==e.badge?p(Ee,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[t]}):t};return()=>p("div",{id:e.id,role:"tab",class:[Sa([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:t.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[$a,Ta]=re("swipe-item");const ja=we(T({name:$a,setup(e,{slots:a}){let t;const l=j({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=Me(pa);if(!o)return;const n=B((()=>{const e={},{vertical:a}=o.props;return o.size.value&&(e[a?"height":"width"]=`${o.size.value}px`),l.offset&&(e.transform=`translate${a?"Y":"X"}(${l.offset}px)`),e})),r=B((()=>{const{loop:e,lazyRender:a}=o.props;if(!a||t)return!0;if(!l.mounted)return!1;const n=o.activeIndicator.value,r=o.count.value-1,s=0===n&&e?r:n-1,u=n===r&&e?0:n+1;return t=i.value===n||i.value===s||i.value===u,t}));return N((()=>{z((()=>{l.mounted=!0}))})),_e({setOffset:e=>{l.offset=e}}),()=>{var e;return p("div",{class:Ta(),style:n.value},[r.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[Ba,za]=re("tab");const Na=we(T({name:Ba,props:pe({},He,{dot:Boolean,name:se,badge:se,title:String,disabled:Boolean,titleClass:Ve,titleStyle:[String,Object],showZeroBadge:xe}),setup(a,{slots:t}){const o=ia(),i=e(!1),n=_(),{parent:r,index:s}=Me(xa);if(!r)return;const u=()=>{var e;return null!=(e=a.name)?e:s.value},d=B((()=>{const e=u()===r.currentName.value;return e&&!i.value&&(i.value=!0,r.props.lazyRender&&z((()=>{r.onRendered(u(),a.title)}))),e})),c=e(""),v=e("");A((()=>{const{titleClass:e,titleStyle:t}=a;c.value=e?la(e):"",v.value=t&&"string"!=typeof t?function(e){let a="";if(!e||Xe(e))return a;for(const t in e){const l=e[t];(Xe(l)||"number"==typeof l)&&(a+=`${t.startsWith("--")?t:Ge(t)}:${l};`)}return a}(Ke(t)):t}));const f=e(!d.value);return l(d,(e=>{e?f.value=!1:$e((()=>{f.value=!0}))})),l((()=>a.title),(()=>{r.setLine(),r.scrollIntoView()})),O(Ia,d),_e({id:o,renderTitle:e=>p(Ca,M({key:o,id:`${r.id}-${s.value}`,ref:r.setTitleRefs(s.value),style:v.value,class:c.value,isActive:d.value,controls:o,scrollable:r.scrollable.value,activeColor:r.props.titleActiveColor,inactiveColor:r.props.titleInactiveColor,onClick:a=>e(n.proxy,s.value,a)},Ue(r.props,["type","color","shrink"]),Ue(a,["dot","badge","title","disabled","showZeroBadge"])),{title:t.title})}),()=>{var e;const a=`${r.id}-${s.value}`,{animated:l,swipeable:n,scrollspy:u,lazyRender:c}=r.props;if(!t.default&&!l)return;const v=u||d.value;if(l||n)return p(ja,{id:o,role:"tabpanel",class:za("panel-wrapper",{inactive:f.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":a,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[p("div",{class:za("panel")},[null==(e=t.default)?void 0:e.call(t)])]}});const m=i.value||u||!c?null==(e=t.default)?void 0:e.call(t):null;return R(p("div",{id:o,role:"tabpanel",class:za("panel"),tabindex:v?0:-1,"aria-labelledby":a,"data-allow-mismatch":"attribute"},[m]),[[E,v]])}}})),qa=we(ka),Aa={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Oa={class:"pc_container",style:{display:"flex"}},Ra={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},Ea={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Ma={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},Va=["src"],Ua=["src"],La={class:"mobile_container"},Ha={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Wa={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},Da=V({__name:"index",setup(f){const g=ee("loading.png"),y=ee("copy.png"),b=j({}),w=t(),x={},I=e([]),_=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),S=e(null),{locale:C}=U(),$=L(),T=e(!1);let z=e("a"),q=e("");const A=e(""),O=e(null),E=e(null);let M;const V=async()=>{var e;await X({appId:w.params.uuid,user:_.value.userName,mode:null==(e=S.value)?void 0:e.mode,task_id:A.value}),setTimeout((()=>{pe.abort(),fe=!0,ce.value=[],ye()}),0)};N((async()=>{var e,t;const l=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${l}px`);if(["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=w.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=w.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),C.value=H(),a.get("userInfo"))_e(),ue();else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=W();e&&"zh-CN"!=e?$.push("/login"):window.addLoginDom()}})),l(q,(()=>{q.value&&(z.value="b")}));const ae=()=>{w.params.uuid&&Z({appId:w.params.uuid,user:_.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(I.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],t=e[a].variable;x[t]={label:e[a].label},b[t]=""})))}))},te=B((()=>!!I.value.length)),le=B((()=>I.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),oe=()=>{w.params.uuid&&Y({appId:w.params.uuid,user:_.value.userName}).then((e=>{S.value={...e}}))},ie=e(!1),ne=e(!1),re=e(!1),se=e(!1),ue=async()=>{var e,a;let t=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=_.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:w.params.uuid,userUuid:null==(a=_.value)?void 0:a.openid}];await D.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",t)},de=()=>{var e,a;if(0!=le.value.length||(t=b,Object.values(t).some((e=>e)))){var t;for(let e in b)if(le.value.includes(e)&&!b[e])return void G({message:`${x[e].label}为必填项！`,type:"error"});(null==(e=S.value)?void 0:e.mode)&&(["advanced-chat","chat"].includes(null==(a=S.value)?void 0:a.mode)?G({type:"success",message:"计划中，敬请期待..."}):"completion"==S.value.mode?ge():he())}else G({message:"请输入您的问题。",type:"error"})},ce=e([]),ve=e(!1);let pe,fe=!1,me=!1;const he=async()=>{q.value="",ce.value=[],ve.value=!1,fe=!1,pe=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run`;0,ie.value=!0,ne.value=!0,se.value=!1,await We(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:_.value.userName,inputs:{...b,outputLanguage:b.outputLanguage?b.outputLanguage:"中文"==K()?"简体中文":K()},files:[],response_mode:"streaming",appUuid:w.params.appUuid}),onmessage(e){var a,t;if(e.data.trim())try{const l=JSON.parse(e.data);if(A.value=l.task_id,l.error)throw new Error(l.error);"text_chunk"===l.event&&(T.value=!0,ce.value.push(null==(a=null==l?void 0:l.data)?void 0:a.text),ve.value||ye()),"workflow_started"===l.event&&(ie.value=!1,re.value=!0),"workflow_finished"===l.event&&(T.value||(ce.value.push(null==(t=null==l?void 0:l.data)?void 0:t.outputs.text),ve.value||ye()),fe=!0,z.value="b")}catch(l){xe(l)}},onerror(e){xe(e)},signal:pe.signal,openWhenHidden:!0})}catch(e){xe()}},ge=async()=>{q.value="",ce.value=[],ve.value=!1,fe=!1,pe=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages`;0,ie.value=!0,ne.value=!0,se.value=!1,await We(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:_.value.userName,inputs:{...b,outputLanguage:K()},files:[],response_mode:"streaming",appUuid:w.params.appUuid}),onmessage(e){if(ie.value=!1,re.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(A.value=a.task_id,a.error)throw new Error(a.error);"message"===a.event&&(ce.value.push(null==a?void 0:a.answer),ve.value||ye()),"message_end"===a.event&&(z.value="b",fe=!0)}catch(a){xe(a)}},onerror(e){xe(e)},signal:pe.signal,openWhenHidden:!0})}catch(e){xe()}},ye=()=>{if(0===ce.value.length)return ve.value=!1,me=!0,void be();ve.value=!0;const e=ce.value.shift();we(e).then((()=>{ye()}))},be=()=>{me&&fe&&(ne.value=!1,re.value=!1,se.value=!0)},we=e=>new Promise((a=>{let t=0;M=setInterval((()=>{if(t<e.length){q.value+=e[t++];const a=document.getElementById("typing-area");a.scrollTop=a.scrollHeight}else clearInterval(M),a()}),15)})),xe=()=>{setTimeout((()=>{pe.abort()}),0),ie.value=!1,re.value=!1,ne.value=!1,ve.value=!1,G.error("访问太火爆了！休息下，请稍后再试！"),q.value="访问太火爆了！休息下，请稍后再试！"},ke=async()=>{try{await navigator.clipboard.writeText(q.value),G({type:"success",message:"复制成功"})}catch(e){G(e)}},Ie=()=>{for(let e in b)b[e]="";O.value.forEach((e=>{e.updateMessage()})),E.value.forEach((e=>{e.updateMessage()}))},_e=()=>{if(localStorage.getItem("yudaoToken"))return ae(),void oe();const e=a.get("userInfo");if(e){const a=JSON.parse(e);try{J({userId:a.userId,userName:a.userName,realName:a.realName,avatar:a.avatar,plaintextUserId:a.plaintextUserId,mobile:a.mobile,email:a.email}).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),ae(),oe())}))}catch(t){}}};return(e,a)=>{const t=k,l=P("v-md-preview"),f=Q;return o(),i("div",Aa,[n("div",Oa,[s(te)?(o(),i(c,{key:0},[n("div",Ra,[(o(!0),i(c,null,v(s(I),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(Pe,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:s(b)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>s(b)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRef",ref:O},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Ea,[p(t,{onClick:Ie},{default:d((()=>[m("Clear")])),_:1}),p(t,{onClick:de,loading:s(ne),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])]),R((o(),i("div",{style:{width:"60%","margin-left":"15px",padding:"15px","border-radius":"10px"},class:F({"bg-[#fff]":s(q)})},[n("div",Ma,[s(q)?(o(),u(l,{key:0,text:s(q),id:"previewMd"},null,8,["text"])):h("",!0)]),n("div",null,[s(re)?(o(),i("img",{key:0,src:s(g),alt:"loading",class:"spinner"},null,8,Va)):h("",!0),s(re)?(o(),i("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:V},r(e.$t("tool.stopGeneration")),1)):h("",!0),s(se)?(o(),i("img",{key:2,onClick:ke,src:s(y),alt:"",style:{width:"20px"},class:"copy"},null,8,Ua)):h("",!0)])],2)),[[f,s(ie)]])],64)):h("",!0)]),n("div",La,[p(s(qa),{active:s(z),shrink:"","line-width":"20"},{default:d((()=>[p(s(Na),{title:"输入",name:"a"},{default:d((()=>[(o(!0),i(c,null,v(s(I),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(Pe,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:s(b)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>s(b)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRefs",ref:E},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Ha,[p(t,{onClick:Ie},{default:d((()=>[m("Clear")])),_:1}),p(t,{onClick:a[0]||(a[0]=e=>de()),loading:s(ne),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])])),_:1}),p(s(Na),{title:"结果",name:"b"},{default:d((()=>[n("div",Wa,[s(q)?(o(),u(l,{key:0,text:s(q),id:"previewMd"},null,8,["text"])):h("",!0)])])),_:1})])),_:1},8,["active"])])])}}},[["__scopeId","data-v-b3219535"]]);export{Da as default};
