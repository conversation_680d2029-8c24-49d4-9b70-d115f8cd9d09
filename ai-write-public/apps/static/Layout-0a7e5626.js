/* empty css                  */import{_ as e}from"./index-da695648.js";import{_ as a,a as t,u as i,r as n,b as s,w as l,o,c as p,d as c,e as d,f as r,g as m,h as u,i as _,j as v,t as g,k as f,l as h,m as b,n as x,p as y,q as I,s as w,E as A,v as E,x as j,y as T}from"./index-51e6c9ce.js";import{g as S}from"./index-0bce936e.js";import{c as V}from"./index-7418a2fe.js";const L=[{name:"writingAssistant",path:"/writingAssistant",type:"医学写作",meta:{title:"论文",dify_app_uuid:"10c03431-1933-4f59-8848-e2e3ccb2c557",desc:"医学论文是医学领域的研究者撰写的，旨在探讨疾病病因、诊断、治疗、预防或医学理论、技术进展的学术性文章。它基于科学实验或临床观察，通过严谨的数据分析，提出新发现、新观点或验证已有理论，为医学实践提供科学依据和指导。",icon:S("AI写作助手.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-53ca1063.js")),["static/index-53ca1063.js","static/Editor-d49de10f.js","static/index-51e6c9ce.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/el-tooltip-4ed993c7.js","static/index-0dfb5b8e.js","static/index-0bce936e.js","static/index-044dd070.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"intelligentPolishing",path:"/intelligentPolishing",type:"医学写作",meta:{title:"综述",dify_app_uuid:"4955c5cc-a366-45d8-8046-aa3044ff77bc",desc:"医学综述是对某一医学领域或专题在一定时期内研究成果的汇总和分析，旨在系统回顾研究进展，评价研究质量，探讨存在问题，预测未来方向，为研究者提供全面、深入的信息参考。",icon:S("AI智能润色.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-602da95e.js")),["static/index-602da95e.js","static/index-51e6c9ce.js","static/index-87571f09.css","static/index-0dfb5b8e.js","static/index-84dd1b27.css","static/el-button-b4242b8c.css","static/el-divider-f4d3946e.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"reviewer",path:"/reviewer",type:"医学写作",meta:{title:"研究方案",dify_app_uuid:"d7df5b59-280e-4dda-b0f2-700a12b0b1f9",desc:"医学研究方案是科研人员为探索医学问题而设计的详细计划，包括研究目的、假设、方法、样本选择、数据收集与分析步骤、预期结果及伦理考量等，确保研究过程科学、严谨、可重复。",icon:S("审稿人回复信.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-d16a9f3a.js")),["static/index-d16a9f3a.js","static/Editor-d49de10f.js","static/index-51e6c9ce.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-0dfb5b8e.js","static/index-0bce936e.js","static/index-cc9df6ae.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"rewrite",path:"/rewrite",type:"医学写作",meta:{title:"软文",dify_app_uuid:"d1fc1b66-47ce-45cd-9ff9-ee49a2e44881",desc:"医学软文是以医疗、健康领域为背景，通过撰写具有吸引力和可读性的文章，间接推广医疗产品、服务或健康理念的一种营销手段。",icon:S("AI降重改写.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-9708cc34.js")),["static/index-9708cc34.js","static/index-0dfb5b8e.js","static/index-51e6c9ce.js","static/index-87571f09.css","static/index-0bce936e.js","static/el-button-b4242b8c.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"topTitle",path:"/topTitle",type:"医学写作",meta:{title:"指南共识",dify_app_uuid:"7252420d-c356-4bed-9e06-ce6c3bc01270",desc:"指南共识是指在医学领域中，基于系统评价的证据和专家经验，针对特定临床问题制定的指导性建议或推荐意见。",icon:S("高分title生成器.svg"),bg:S("题材和论文章节的写作语料.png")},component:()=>a((()=>import("./index-82ae96d2.js")),["static/index-82ae96d2.js","static/el-tooltip-4ed993c7.js","static/index-0dfb5b8e.js","static/index-51e6c9ce.js","static/index-87571f09.css","static/index-0bce936e.js","static/index-19af116e.css","static/el-button-b4242b8c.css","static/el-popper-27830cce.css","static/el-divider-f4d3946e.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"病例报告",dify_app_uuid:"05544f18-9efb-42e4-ad5d-8ca78feaf56c",desc:"病例报告是详细记录并分析个别患者疾病发生、诊断、治疗及转归过程的医学文献，用于探讨罕见病、特殊临床表现或治疗经验。",icon:S("综述Review.svg"),bg:S("题材和论文章节的写作语料.png")},component:()=>a((()=>import("./index-9a9827a5.js")),["static/index-9a9827a5.js","static/Editor-d49de10f.js","static/index-51e6c9ce.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-0dfb5b8e.js","static/index-0bce936e.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"国自然基金写作",dify_app_uuid:"cc0c4388-8c02-4f1c-8a32-e0f3e84146c7",desc:"“国自然基金写作”是撰写国家自然科学基金申请书的过程，该过程需遵循科学基金的撰写要求和规范，涵盖项目名称、关键词、摘要、立项依据、研究目标、内容、方案及创新性等多个方面的撰写，旨在清晰、准确、有逻辑地展示研究项目的科学性、创新性和可行性，以获得基金资助。",icon:S("综述Review.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-9a9827a5.js")),["static/index-9a9827a5.js","static/Editor-d49de10f.js","static/index-51e6c9ce.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-0dfb5b8e.js","static/index-0bce936e.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"学术写作问答",dify_app_uuid:"c81251e7-9269-413b-95d3-0a4b88ce84b9",desc:"",icon:S("场景写作.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-9a9827a5.js")),["static/index-9a9827a5.js","static/Editor-d49de10f.js","static/index-51e6c9ce.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-0dfb5b8e.js","static/index-0bce936e.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"AI产品发布会",dify_app_uuid:"5ec1c9f5-a9a3-4697-bb66-71793e8375a6",desc:"",icon:S("场景写作.svg"),bg:S("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-9a9827a5.js")),["static/index-9a9827a5.js","static/Editor-d49de10f.js","static/index-51e6c9ce.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-0dfb5b8e.js","static/index-0bce936e.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])}],O=e=>(j("data-v-0bee7529"),e=e(),T(),e),P={class:"flex-1 flex flex-col overflow-hidden"},R={class:"text-gray-500 mb-2"},D=["href"],k=O((()=>v("span",{class:"line"},"/",-1))),C=O((()=>v("span",{class:"line"},"/",-1))),N={class:"flex items-center my-2"},$=["src"],U={class:"text-xl font-bold text-dark-200 mr-4"},z={class:"flex-1 overflow-hidden"},F={class:"flex items-center my-2"},H=["src"],J={class:"text-xl font-bold text-dark-200"},q={class:"text-dark-500 mb-6"},K={class:"flex justify-center"},Z=t({__name:"Layout",setup(a){var t;const j=i(),T=n([]),S=n(null),O=n(null),Z=n(!1),B=n(0),G=n(!1),{locale:M}=s(),Q=navigator.browserLanguage||navigator.language,W=(null==(t=j.params)?void 0:t.lang)||Q,X=n();M.value=W;const Y=()=>{Z.value=!0},ee=e=>{G.value=e};l((()=>j),(()=>{O.value=JSON.parse(sessionStorage.getItem("nodeInfo")),S.value.setCurrentKey(node.name)}),{deep:!0});const ae=n({appUuid:j.params.appUuid});return o((async()=>{X.value="https://ai.medsci.cn",await void w(ae.value).then((e=>{O.value=e[0]})).catch((e=>{})),"zh-CN"==localStorage.getItem("ai_apps_lang")&&(G.value=!0),L.map((e=>(e.label=e.meta.title,{label:e.type,children:L.filter((a=>a.type==e.type))}))).forEach((e=>{T.value.find((a=>a.type===e.label))||T.value.push(e)})),sessionStorage.getItem("nodeInfo")?O.value=JSON.parse(sessionStorage.getItem("nodeInfo")):O.value=T.value[0].children[0],B.value=document.body.clientHeight-56})),(a,t)=>{var i,n,s,l,o,w;const j=e,T=A,S=p("router-view"),L=E;return c(),d("div",null,[r(j,{onIsZHChange:ee}),m(G)?(c(),u(V,{key:0})):_("",!0),v("div",{class:"h-full flex p-6",style:I({height:`${m(B)}px`})},[v("main",P,[v("div",null,[v("div",R,[v("a",{class:"cursor-pointer hover:text-[#5298FF]",href:m(X)},g(a.$t("tool.home")),9,D),k,f(g((null==(i=m(O))?void 0:i.appType)?a.$t(`${m(h)[null==(n=m(O))?void 0:n.appType]}`):""),1),C,f(g(null==(s=m(O))?void 0:s.appName),1)]),v("div",N,[v("img",{class:"w-[38px] h-[38px] mr-4",src:(null==(l=m(O))?void 0:l.appIcon)?null==(o=m(O))?void 0:o.appIcon:"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,$),v("div",U,g(null==(w=m(O))?void 0:w.appName),1),r(T,{plain:"",size:"small",onClick:Y},{default:b((()=>[f(g(a.$t("tool.intro")),1)])),_:1})])]),v("div",z,[r(S,null,{default:b((({Component:e})=>[(c(),u(x(e)))])),_:1})])]),r(L,{modelValue:m(Z),"onUpdate:modelValue":t[1]||(t[1]=e=>y(Z)?Z.value=e:null),"show-close":!1,width:"500"},{header:b((()=>{var e,a,t;return[v("div",F,[v("img",{class:"w-[38px] h-[38px] mr-4",src:(null==(e=m(O))?void 0:e.appIcon)?null==(a=m(O))?void 0:a.appIcon:"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,H),v("div",J,g(null==(t=m(O))?void 0:t.appName),1)])]})),default:b((()=>{var e;return[v("div",q,g(null==(e=m(O))?void 0:e.appDescription),1),v("div",K,[r(T,{onClick:t[0]||(t[0]=e=>Z.value=!1)},{default:b((()=>[f("我知道了")])),_:1})])]})),_:1},8,["modelValue"])],4)])}}},[["__scopeId","data-v-0bee7529"]]);export{Z as default};
