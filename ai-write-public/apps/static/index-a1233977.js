import{a as e,E as o,K as t,m as s,n as a,p as n,e as l,V as r,t as i,M as c,q as u,o as d,W as p,B as v,h as m,f as y,F as f,x as g,X as h,J as x,Y as b,N as w,Z as S}from"./use-touch-2f0cafe6.js";import{M as k,aw as O,O as z,f as C,W as I,K as B,L as P,w as L,r as T,I as j,ab as A,ax as E,U as N,V as R,n as $,o as D,P as F,T as H,F as K,ai as M}from"https://static.medsci.cn/ai-write/static/index-fe292e2d.js";let V=2e3;const[U,W]=e("config-provider"),Y=Symbol(U),[q,J]=e("icon");const X=n(k({name:q,props:{dot:Boolean,tag:s("i"),name:String,size:a,badge:a,color:String,badgeProps:Object,classPrefix:String},setup(e,{slots:s}){const a=O(Y,null),n=z((()=>e.classPrefix||(null==a?void 0:a.iconPrefix)||J()));return()=>{const{tag:a,dot:l,name:r,size:i,badge:c,color:u}=e,d=(e=>null==e?void 0:e.includes("/"))(r);return C(t,I({dot:l,tag:a,class:[n.value,d?"":`${n.value}-${r}`],style:{color:u,fontSize:o(i)},content:c},e.badgeProps),{default:()=>{var e;return[null==(e=s.default)?void 0:e.call(s),d&&C("img",{class:J("image"),src:r},null)]}})}}})),[Z,G]=e("loading"),Q=Array(12).fill(null).map(((e,o)=>C("i",{class:G("line",String(o+1))},null))),_=C("svg",{class:G("circular"),viewBox:"25 25 50 50"},[C("circle",{cx:"50",cy:"50",r:"20",fill:"none"},null)]);const ee=n(k({name:Z,props:{size:a,type:s("circular"),color:String,vertical:Boolean,textSize:a,textColor:String},setup(e,{slots:t}){const s=z((()=>l({color:e.color},r(e.size)))),a=()=>{const o="spinner"===e.type?Q:_;return C("span",{class:G("spinner",e.type),style:s.value},[t.icon?t.icon():o])},n=()=>{var s;if(t.default)return C("span",{class:G("text"),style:{fontSize:o(e.textSize),color:null!=(s=e.textColor)?s:e.color}},[t.default()])};return()=>{const{type:o,vertical:t}=e;return C("div",{class:G([o,{vertical:t}]),"aria-live":"polite","aria-busy":!0},[a(),n()])}}})),oe={show:Boolean,zIndex:a,overlay:i,duration:a,teleport:[String,Object],lockScroll:i,lazyRender:i,beforeClose:Function,overlayStyle:Object,overlayClass:c,transitionAppear:Boolean,closeOnClickOverlay:i},te=Object.keys(oe);let se=0;const ae="van-overflow-hidden";function ne(e){const o=T(!1);return L(e,(e=>{e&&(o.value=e)}),{immediate:!0}),e=>()=>o.value?e():null}const le=()=>{var e;const{scopeId:o}=(null==(e=j())?void 0:e.vnode)||{};return o?{[o]:""}:null},[re,ie]=e("overlay");const ce=n(k({name:re,props:{show:Boolean,zIndex:a,duration:a,className:c,lockScroll:i,lazyRender:i,customStyle:Object,teleport:[String,Object]},setup(e,{slots:o}){const t=T(),s=ne((()=>e.show||!e.lazyRender))((()=>{var s;const a=l(y(e.zIndex),e.customStyle);return f(e.duration)&&(a.animationDuration=`${e.duration}s`),N(C("div",{ref:t,style:a,class:[ie(),e.className]},[null==(s=o.default)?void 0:s.call(o)]),[[R,e.show]])}));return m("touchmove",(o=>{e.lockScroll&&v(o,!0)}),{target:t}),()=>{const o=C(A,{name:"van-fade",appear:!0},{default:s});return e.teleport?C(E,{to:e.teleport},{default:()=>[o]}):o}}})),ue=l({},oe,{round:Boolean,position:s("center"),closeIcon:s("cross"),closeable:Boolean,transition:String,iconPrefix:String,closeOnPopstate:Boolean,closeIconPosition:s("top-right"),safeAreaInsetTop:Boolean,safeAreaInsetBottom:Boolean}),[de,pe]=e("popup");const ve=n(k({name:de,inheritAttrs:!1,props:ue,emits:["open","close","opened","closed","keydown","update:show","clickOverlay","clickCloseIcon"],setup(e,{emit:o,attrs:t,slots:s}){let a,n;const l=T(),r=T(),i=ne((()=>e.show||!e.lazyRender)),c=z((()=>{const o={zIndex:l.value};if(f(e.duration)){o["center"===e.position?"animationDuration":"transitionDuration"]=`${e.duration}s`}return o})),y=()=>{a||(a=!0,l.value=void 0!==e.zIndex?+e.zIndex:++V,o("open"))},w=()=>{a&&x(e.beforeClose,{done(){a=!1,o("close"),o("update:show",!1)}})},S=t=>{o("clickOverlay",t),e.closeOnClickOverlay&&w()},k=()=>{if(e.overlay)return C(ce,I({show:e.show,class:e.overlayClass,zIndex:l.value,duration:e.duration,customStyle:e.overlayStyle,role:e.closeOnClickOverlay?"button":void 0,tabindex:e.closeOnClickOverlay?0:void 0},le(),{onClick:S}),{default:s["overlay-content"]})},O=e=>{o("clickCloseIcon",e),w()},j=()=>{if(e.closeable)return C(X,{role:"button",tabindex:0,name:e.closeIcon,class:[pe("close-icon",e.closeIconPosition),b],classPrefix:e.iconPrefix,onClick:O},null)};let M;const U=()=>{M&&clearTimeout(M),M=setTimeout((()=>{o("opened")}))},W=()=>o("closed"),Y=e=>o("keydown",e),q=i((()=>{var o;const{round:a,position:n,safeAreaInsetTop:l,safeAreaInsetBottom:i}=e;return N(C("div",I({ref:r,style:c.value,role:"dialog",tabindex:0,class:[pe({round:a,[n]:n}),{"van-safe-area-top":l,"van-safe-area-bottom":i}],onKeydown:Y},t,le()),[null==(o=s.default)?void 0:o.call(s),j()]),[[R,e.show]])})),J=()=>{const{position:o,transition:t,transitionAppear:s}=e;return C(A,{name:t||("center"===o?"van-fade":`van-popup-slide-${o}`),appear:s,onAfterEnter:U,onAfterLeave:W},{default:q})};return L((()=>e.show),(e=>{e&&!a&&(y(),0===t.tabindex&&$((()=>{var e;null==(e=r.value)||e.focus()}))),!e&&a&&(a=!1,o("close"))})),g({popupRef:r}),function(e,o){const t=u(),s=o=>{t.move(o);const s=t.deltaY.value>0?"10":"01",a=p(o.target,e.value),{scrollHeight:n,offsetHeight:l,scrollTop:r}=a;let i="11";0===r?i=l>=n?"00":"01":r+l>=n&&(i="10"),"11"===i||!t.isVertical()||parseInt(i,2)&parseInt(s,2)||v(o,!0)},a=()=>{document.addEventListener("touchstart",t.start),document.addEventListener("touchmove",s,{passive:!1}),se||document.body.classList.add(ae),se++},n=()=>{se&&(document.removeEventListener("touchstart",t.start),document.removeEventListener("touchmove",s),se--,se||document.body.classList.remove(ae))},l=()=>o()&&n();d((()=>o()&&a())),B(l),P(l),L(o,(e=>{e?a():n()}))}(r,(()=>e.show&&e.lockScroll)),m("popstate",(()=>{e.closeOnPopstate&&(w(),n=!1)})),D((()=>{e.show&&y()})),F((()=>{n&&(o("update:show",!0),n=!1)})),B((()=>{e.show&&e.teleport&&(w(),n=!0)})),H(h,(()=>e.show)),()=>e.teleport?C(E,{to:e.teleport},{default:()=>[k(),J()]}):C(K,null,[k(),J()])}}));let me=0;const[ye,fe]=e("toast"),ge=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay","zIndex"];var he=k({name:ye,props:{icon:String,show:Boolean,type:s("text"),overlay:Boolean,message:a,iconSize:a,duration:S(2e3),position:s("middle"),teleport:[String,Object],wordBreak:String,className:c,iconPrefix:String,transition:s("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:c,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean,zIndex:a},emits:["update:show"],setup(e,{emit:o,slots:t}){let s,a=!1;const n=()=>{const o=e.show&&e.forbidClick;a!==o&&(a=o,a?(me||document.body.classList.add("van-toast--unclickable"),me++):me&&(me--,me||document.body.classList.remove("van-toast--unclickable")))},l=e=>o("update:show",e),r=()=>{e.closeOnClick&&l(!1)},i=()=>clearTimeout(s),c=()=>{const{icon:o,type:t,iconSize:s,iconPrefix:a,loadingType:n}=e;return o||"success"===t||"fail"===t?C(X,{name:o||t,size:s,class:fe("icon"),classPrefix:a},null):"loading"===t?C(ee,{class:fe("loading"),size:s,type:n},null):void 0},u=()=>{const{type:o,message:s}=e;return t.message?C("div",{class:fe("text")},[t.message()]):f(s)&&""!==s?"html"===o?C("div",{key:0,class:fe("text"),innerHTML:String(s)},null):C("div",{class:fe("text")},[s]):void 0};return L((()=>[e.show,e.forbidClick]),n),L((()=>[e.show,e.type,e.message,e.duration]),(()=>{i(),e.show&&e.duration>0&&(s=setTimeout((()=>{l(!1)}),e.duration))})),D(n),M(n),()=>C(ve,I({class:[fe([e.position,"normal"===e.wordBreak?"break-normal":e.wordBreak,{[e.type]:!e.icon}]),e.className],lockScroll:!1,onClick:r,onClosed:i,"onUpdate:show":l},w(e,ge)),{default:()=>[c(),u()]})}});const xe=n(he);export{X as I,ee as L,ve as P,xe as T,te as a,oe as p,he as s};
