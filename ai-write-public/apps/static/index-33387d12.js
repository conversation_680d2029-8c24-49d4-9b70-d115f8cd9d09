/* empty css                  *//* empty css                *//* empty css                 */import{s as e}from"./index-4e652767.js";import{r as a,aL as l,c as s,d as t,e as u,f as i,p as r,i as n,l as o,j as p,F as v,y as m,S as c,g as d,aM as f,h as x,a3 as y,B as j,E as h,G as g}from"./index-732573c8.js";import{s as w}from"./index-d4a1c4f2.js";/* empty css                   */const k={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},S=["innerHTML"],b={class:"flex justify-center mt-10"},E={__name:"index",setup(E){const L=a(""),M=a([]),V=a([]),z=a(5),A=()=>{if(a=L.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!L.value)return y.warning("请输入英文内容");var a;e({text:L.value}).then((e=>{e&&e&&e.data&&(M.value=e.data,M.value=w(M.value),z.value=5,B())}))},B=()=>{let e=[];5==z.value?(e=[0,5],z.value=3):3==z.value?(e=[5,8],z.value=2):2==z.value&&(e=[8,10],z.value=5),V.value=JSON.parse(JSON.stringify(M.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,w=h,E=g,M=l("copy");return s(),t("div",k,[u(y,{modelValue:i(L),"onUpdate:modelValue":a[0]||(a[0]=e=>r(L)?L.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(w,{type:"primary",onClick:A},{default:o((()=>[p("改 写")])),_:1})]),i(V)&&i(V).length?(s(),t("div",_,[(s(!0),t(v,null,m(i(V),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,S),c((s(),d(E,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(i(f))])),_:2},1024)),[[M,e.text]])])))),128)),n("div",b,[u(w,{type:"primary",link:"",onClick:B},{default:o((()=>[p("换一换")])),_:1})])])):x("",!0)])}}};export{E as default};
