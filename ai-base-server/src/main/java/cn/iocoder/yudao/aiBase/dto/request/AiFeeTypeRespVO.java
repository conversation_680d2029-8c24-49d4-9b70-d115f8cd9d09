package cn.iocoder.yudao.aiBase.dto.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "管理后台 - AI费用类型 Response VO")
@Data
public class AiFeeTypeRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("主键")
    private Integer id;

    @Schema(description = "应用uuid", requiredMode = Schema.RequiredMode.REQUIRED, example = "app-uuid-123")
    @ExcelProperty("应用UUID")
    private String appUuid;

    @Schema(description = "应用语言", example = "zh")
    @ExcelProperty("应用语言")
    private String lang;

    @Schema(description = "订阅套餐，一般是appNameEn", requiredMode = Schema.RequiredMode.REQUIRED, example = "ai-writer")
    @ExcelProperty("订阅套餐")
    private String packageKey;

    @Schema(description = "订阅方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "连续包月")
    @ExcelProperty("订阅方式")
    private String packageType;

    @Schema(description = "周期类型，默认月", example = "MONTH")
    private String periodType;

    @Schema(description = "时长", example = "1")
    private Integer monthNum;

    @Schema(description = "原价格", example = "99.00")
    private BigDecimal oldPrice;

    @Schema(description = "价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "29.90")
    @ExcelProperty("价格")
    private BigDecimal feePrice;

    @Schema(description = "币种", example = "人民币")
    @ExcelProperty("币种")
    private String coinType;

    @Schema(description = "价格ID，币种是人民币的是支付宝场景值，美元的是stripe", example = "price_123")
    @ExcelProperty("价格ID")
    private String priceId;

    @Schema(description = "可使用次数，-1不限制", example = "100")
    @ExcelProperty("可使用次数")
    private Integer num;

    @Schema(description = "1开启，0下架", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("在线状态")
    private Integer online;

    @Schema(description = "支付宝订阅，stripe订阅，按次收费", example = "支付宝订阅")
    private String priceType;

    @Schema(description = "购买后的有效期", example = "12")
    private Integer expiredMonths;

    @Schema(description = "排序，越小越前", example = "1")
    private Integer orderNum;

    @Schema(description = "备注", example = "这是一个测试费用类型")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;

}
