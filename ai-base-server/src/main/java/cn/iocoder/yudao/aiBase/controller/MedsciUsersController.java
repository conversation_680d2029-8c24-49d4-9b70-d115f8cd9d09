package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.dto.param.*;
import cn.iocoder.yudao.aiBase.dto.request.*;
import cn.iocoder.yudao.aiBase.dto.request.dify.AccountRequest;
import cn.iocoder.yudao.aiBase.dto.response.AiSubOrdersResponse;
import cn.iocoder.yudao.aiBase.dto.response.AiSubscriptionLogResponse;
import cn.iocoder.yudao.aiBase.dto.response.UserPackageResponse;
import cn.iocoder.yudao.aiBase.dto.response.admin.KnowledgeResponse;
import cn.iocoder.yudao.aiBase.dto.response.admin.UserAppResponse;
import cn.iocoder.yudao.aiBase.entity.*;
import cn.iocoder.yudao.aiBase.service.*;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 主站用户")
@RestController
@RequestMapping("/admin-api/medsciUsers")
@Validated
@Slf4j
public class MedsciUsersController {

    @Resource
    private MedsciUsersService usersService;

    @Resource
    private AiSubscriptionLogService aiSubscriptionLogService;

    @Resource
    private AiStripeEventLogService aiStripeEventLogService;

    @Resource
    private AiSubOrdersService aiSubOrdersService;

    @Resource
    private YudaoSystemService yudaoSystemService;

    @Resource
    private ApiTokensService apiTokensService;

    @Resource
    private AiAppUserPackageService aiPackageService;

    @Resource
    private AiAppUsersService aiAppUsersService;

    @Resource
    private AiAppLangsService aiAppLangsService;

    @Resource
    private AiAppKnowledgeFileService knowledgeFileService;

    @PostMapping("/create")
    @Operation(summary = "创建主站用户")
    @PreAuthorize("@ss.hasPermission('medsci:users:create')")
    public CommonResult<Long> createUsers(@Valid @RequestBody MedsciUsersSaveReqVO createReqVO) {
        return CommonResult.success(usersService.createUsers(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新主站用户")
    @PreAuthorize("@ss.hasPermission('medsci:users:update')")
    public CommonResult<Boolean> updateUsers(@Valid @RequestBody MedsciUsersSaveReqVO updateReqVO) {
        usersService.updateUsers(updateReqVO);
        return CommonResult.success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除主站用户")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('medsci:users:delete')")
    public CommonResult<Boolean> deleteUsers(@RequestParam("id") Integer id) {
        usersService.deleteUsers(id);
        return CommonResult.success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得主站用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<MedsciUsersRespVO> getUsers(@RequestParam("id") Integer id) {
        MedsciUsers users = usersService.getUsers(id);
        return CommonResult.success(BeanUtils.toBean(users, MedsciUsersRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得主站用户分页")
    public CommonResult<PageResult<MedsciUsersRespVO>> getUsersPage(@Valid MedsciUsersPageReqVO pageReqVO) {
        PageResult<MedsciUsers> pageResult = usersService.selectPage(pageReqVO);
        return CommonResult.success(BeanUtils.toBean(pageResult, MedsciUsersRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出主站用户 Excel")
    @PreAuthorize("@ss.hasPermission('medsci:users:export')")
    @ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
    public void exportUsersExcel(@Valid MedsciUsersPageReqVO pageReqVO,
                                 HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MedsciUsers> list = usersService.selectPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "主站用户.xls", "数据", MedsciUsersRespVO.class,
            BeanUtils.toBean(list, MedsciUsersRespVO.class));
    }

    @GetMapping("/subLogPage")
    @Operation(summary = "订阅链接分页")
    public CommonResult<PageResult<AiSubscriptionLogResponse>> subLogPage(@Valid AppUserPageReqVO pageReqVO) {
        PageResult<AiSubscriptionLog> pageResult = aiSubscriptionLogService.selectPage(pageReqVO);
        return CommonResult.success(BeanUtils.toBean(pageResult, AiSubscriptionLogResponse.class, item -> {
            item.setUserName(usersService.getUserName(item.getSocialUserId(), item.getSocialType()));
        }));
    }

    @GetMapping("/eventLogPage")
    @Operation(summary = "事件分页")
    public CommonResult<PageResult<AiStripeEventLog>> eventLogPage(@Valid EventPageReqVO pageReqVO) {
        PageResult<AiStripeEventLog> pageResult = aiStripeEventLogService.selectPage(pageReqVO);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/subOrderPage")
    @Operation(summary = "订单分页")
    public CommonResult<PageResult<AiSubOrdersResponse>> subOrderPage(@Valid AppUserPageReqVO pageReqVO) {
        PageResult<AiSubOrders> pageResult = aiSubOrdersService.selectPage(pageReqVO);
        return CommonResult.success(BeanUtils.toBean(pageResult, AiSubOrdersResponse.class, item -> {
            item.setUserName(usersService.getUserName(item.getSocialUserId(), item.getSocialType()));
        }));
    }

    @GetMapping("/userPackagePage")
    @Operation(summary = "中文订阅套餐")
    public CommonResult<PageResult<UserPackageResponse>> userPackagePage(@Valid UserPackageParam pageReqVO) {
        PageResult<AiAppUserPackage> pageResult = aiPackageService.selectPage(pageReqVO);
        return CommonResult.success(BeanUtils.toBean(pageResult, UserPackageResponse.class, item -> {
            item.setUserName(usersService.getUserName(item.getSocialUserId(), item.getSocialType()));
        }));
    }

    @GetMapping("/userAppPage")
    @Operation(summary = "用户应用")
    public CommonResult<PageResult<UserAppResponse>> userAppPage(@Valid AppUserParam pageReqVO) {
        PageResult<AiAppUsers> pageResult = aiAppUsersService.selectPage(pageReqVO);
        Map<String, AiAppLangs> aiAppMap = aiAppLangsService.selectMap(AiAppParam.builder().build());
        return CommonResult.success(BeanUtils.toBean(pageResult, UserAppResponse.class, item -> {
            item.setUserName(usersService.getUserName(item.getSocialUserId(), item.getSocialType()));
            AiAppLangs aiAppLangs = aiAppMap.getOrDefault(item.getAppUuid(), new AiAppLangs());
            item.setAppName(aiAppLangs.getAppName());
            item.setAppNameEn(aiAppLangs.getAppNameEn());
        }));
    }

    @GetMapping("/knowledgePage")
    @Operation(summary = "知识库")
    public CommonResult<PageResult<KnowledgeResponse>> knowledgePage(@Valid KnowledgeParam pageReqVO) {
        PageResult<AiAppKnowledgeFile> pageResult = knowledgeFileService.selectPage(pageReqVO);
        return CommonResult.success(BeanUtils.toBean(pageResult, KnowledgeResponse.class, item -> {
            item.setUserName(usersService.getUserName(item.getSocialUserId(), item.getSocialType()));
        }));
    }

    @GetMapping("/clearConfigCache")
    @Operation(summary = "清除配置缓存")
    public CommonResult<?> clearConfigCache(@RequestParam("name") String name) {
        try {
            yudaoSystemService.clearConfigCache(name);
            return CommonResult.success(null);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/activeDifyAccount")
    @Operation(summary = "激活dify账号")
    public CommonResult<Integer> activeDifyAccount(@Valid @RequestBody AccountRequest reqVO) {
        return CommonResult.success(apiTokensService.activeDifyAccount(reqVO.getEmail(), reqVO.getName(), reqVO.getPassword()));
    }

    @GetMapping("/getTemplate")
    @Operation(summary = "获得导入用户模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<UserImportExcelVO> list = Arrays.asList(
                UserImportExcelVO.builder().socialType(BaseConstant.ZERO).socialUserId(1L).openid("主站加密id")
                        .userName("zhangsan").email("<EMAIL>")
                        .realName("张三").mobile("***********").avatar("https://img.medsci.cn/web/img/user_icon.png").build()
        );
        // 输出
        ExcelUtils.write(response, "用户导入模板.xlsx", "用户列表", UserImportExcelVO.class, list);
    }

    @PostMapping("/importUser")
    @Operation(summary = "导入用户")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true)
    })
    public CommonResult<List<UserImportExcelVO>> importUser(@RequestParam("file") MultipartFile file) throws Exception {
        List<UserImportExcelVO> list = ExcelUtils.read(file, UserImportExcelVO.class);
        List<UserImportExcelVO> res = usersService.importUser(list);
        return success(res);
    }

    @PostMapping("/importPackage")
    @Operation(summary = "导入用户套餐")
    public CommonResult<List<UserImportExcelVO>> importPackage(@RequestBody ImportPackageReqVO reqVO) {
        try {
            MsUserParam userParam = MsUserParam.builder().socialType(reqVO.getSocialType())
                    .socialUserIds(JSONObject.parseArray(reqVO.getSocialUserIdStr(), Long.class)).build();
            List<MedsciUsers> list = usersService.selectList(userParam);
            reqVO.setStartAt(reqVO.getStartAt()==null? LocalDateTime.now():reqVO.getStartAt());
            for (MedsciUsers medsciUsers : list) {
                try {
                    aiPackageService.initUserPackage(reqVO.getLocale(), medsciUsers.getSocialUserId(), medsciUsers.getSocialType(),
                            reqVO.getPackageKey(), reqVO.getPackageType(), reqVO.getStartAt(), reqVO.getExpireAt());
                } catch (Exception e) {
                    log.error("导入用户套餐失败：{}", e.getMessage());
                }
            }
            return CommonResult.success(null);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @GetMapping("/downloadFile")
    @Operation(summary = "下载知识库")
    @Parameters({
        @Parameter(name = "id", description = "知识库目录ID", required = true, example = "1"),
        @Parameter(name = "userName", description = "用户名", required = false, example = "zhangsan")
    })
    public void downloadFile(@RequestParam("id") Integer id,
                           @RequestParam(value = "userName", required = false) String userName,
                           HttpServletResponse response) {
        try {
            log.info("开始下载知识库目录，ID: {}, 用户名: {}", id, userName);
            knowledgeFileService.downloadFile(id, userName, response);
            log.info("知识库目录下载完成，ID: {}, 用户名: {}", id, userName);
        } catch (Exception e) {
            log.error("下载知识库目录失败，ID: {}, 用户名: {}, 错误信息: {}", id, userName, e.getMessage(), e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"下载失败: " + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败: {}", ioException.getMessage());
            }
        }
    }

    @PostMapping("/expireUserPackage")
    @Operation(summary = "更新用户套餐状态为过期")
    @Parameter(name = "id", description = "用户套餐ID", required = true, example = "1")
    public CommonResult<Boolean> expireUserPackage(@RequestParam("id") Integer id) {
        try {
            AiAppUserPackage userPackage = new AiAppUserPackage();
            userPackage.setId(id);
            userPackage.setSubStatus(BaseConstant.TWO);
            userPackage.setUpdatedAt(LocalDateTime.now());
            aiPackageService.updateById(userPackage);

            return CommonResult.success(true);
        } catch (Exception e) {
            log.error("更新用户套餐状态失败，ID: {}, 错误信息: {}", id, e.getMessage(), e);
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), "更新用户套餐状态失败: " + e.getMessage());
        }
    }

    @PostMapping("/expireUserApp")
    @Operation(summary = "更新用户APP状态为过期")
    @Parameter(name = "id", description = "用户APP ID", required = true, example = "1")
    public CommonResult<Boolean> expireUserApp(@RequestParam("id") Integer id) {
        try {
            AiAppUsers appUser = new AiAppUsers();
            appUser.setId(id);
            appUser.setStatus(BaseConstant.TWO);
            appUser.setUpdatedAt(LocalDateTime.now());
            aiAppUsersService.updateById(appUser);

            return CommonResult.success(true);
        } catch (Exception e) {
            log.error("更新用户APP状态失败，ID: {}, 错误信息: {}", id, e.getMessage(), e);
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), "更新用户APP状态失败: " + e.getMessage());
        }
    }

    //

}
