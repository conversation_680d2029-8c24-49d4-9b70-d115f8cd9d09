package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.dto.param.MsUserParam;
import cn.iocoder.yudao.aiBase.dto.request.*;
import cn.iocoder.yudao.aiBase.dto.request.dify.MedsciUserRequest;
import cn.iocoder.yudao.aiBase.dto.response.FacebookUserInfo;
import cn.iocoder.yudao.aiBase.dto.response.GoogleUserInfo;
import cn.iocoder.yudao.aiBase.dto.response.PurchaseRecord;
import cn.iocoder.yudao.aiBase.dto.response.TokenResponse;
import cn.iocoder.yudao.aiBase.entity.MedsciUsers;
import cn.iocoder.yudao.aiBase.mapper.MedsciUsersMapper;
import cn.iocoder.yudao.aiBase.service.*;
import cn.iocoder.yudao.aiBase.util.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stripe.model.Customer;
import com.xingyuv.jushauth.config.AuthDefaultSource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Slf4j
@Service
@DS(DBConstant.AiBase)
public class MedsciUsersServiceImpl extends ServiceImpl<MedsciUsersMapper, MedsciUsers> implements MedsciUsersService {

    public static final Integer TRIAL_DAY = BaseConstant.ZERO; // 试用期
    public static final Integer ENABLE = BaseConstant.ONE;
    public static final Integer DISABLE = BaseConstant.TWO;
    public static final List<Integer> MEDSCI_TYPES = Arrays.asList(
            SocialTypeEnum.MEDSCI.getType(),
            SocialTypeEnum.GOOGLE.getType(),
            SocialTypeEnum.FACEBOOK.getType(),
            SocialTypeEnum.MEDSCI_AI.getType()
    );

    public static final List<Integer> MEDXY_TYPES = Arrays.asList(
            SocialTypeEnum.MEDXY_GG.getType(),
            SocialTypeEnum.MEDXY_FB.getType(),
            SocialTypeEnum.MEDXY_AI.getType()
    );

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RedisManage redisManage;

    @Autowired
    private OauthService oauthService;

    @Autowired
    private YudaoSystemService yudaoSystemService;

    @Value("${spring.profiles.active}")
    private String active;

    @Value("${dify-base.openapi-host}")
    private String openapiHost;

    @Override
    public MedsciUsers getUser(MedsciUserRequest param, String locale) {
        if (StringUtils.isBlank(param.getAvatar()) || !param.getAvatar().startsWith("http")) {
            param.setAvatar("https://img.medsci.cn/web/img/user_icon.png");
        }
        MedsciUsers user = getBySocialUserIdAndOpenid(param.getSocialUserId(), param.getOpenid());
        if (user == null) {
            user = BeanUtils.toBean(param, MedsciUsers.class);
            user.setStatus(TRIAL_DAY>0 ? ENABLE : DISABLE);
            user.setExpireAt(LocalDateTime.now().plusDays(TRIAL_DAY));
            user.setPassword(passwordEncoder.encode(LocalDateTime.now().format(CommonUtil.DateTimeFormat4)+"@aiBase"));
            this.save(user);
            user = getBySocialUserIdAndOpenid(param.getSocialUserId(), param.getOpenid());
        } else {
            if (StringUtils.isNotBlank(param.getRealName())) {
                user.setRealName(param.getRealName());
            }
            if (StringUtils.isNotBlank(param.getUserName())) {
                user.setUserName(param.getUserName());
            }
            if (StringUtils.isNotBlank(param.getMobile())) {
                user.setMobile(param.getMobile());
            }
            if (StringUtils.isNotBlank(param.getEmail())) {
                user.setEmail(param.getEmail());
            }
            if (StringUtils.isNotBlank(param.getAvatar())) {
                user.setAvatar(param.getAvatar());
            }
            baseMapper.updateById(user);
            updateUserStatus(user);
        }

        // 更新缓存
        redisManage.updateIsInternalUser(user.getSocialType(), user.getSocialUserId(), user.getIsInternalUser());
        redisManage.setUserName(user.getSocialType(), user.getSocialUserId(), user.getUserName());
        return user;
    }

    @Override
    public Long createUsers(MedsciUsersSaveReqVO createReqVO) {
        MedsciUsers other = null;
        // 1.判断socialUserId和socialType 是否已存在
        if (createReqVO.getSocialUserId() != null && createReqVO.getSocialType() != null) {
            other = getBySocialUserId(createReqVO.getSocialType(), createReqVO.getSocialUserId());
            if (other != null) {
                throw exception(ErrorCodeConstants.ERROR_5058);
            }
        }
        // 2.判断mobile 是否已存在
        if (createReqVO.getMobile() != null) {
            other = getByMobile(createReqVO.getSocialType(), createReqVO.getMobile());
            if (other != null) {
                throw exception(ErrorCodeConstants.ERROR_5059);
            }
        }
        // 3.判断email 是否已存在
        if (createReqVO.getEmail() != null) {
            other = getByEmail(createReqVO.getEmail(), createReqVO.getSocialType());
            if (other != null) {
                throw exception(ErrorCodeConstants.ERROR_5003);
            }
        }

        // 4.获取主站用户信息
        try {
            if (SocialTypeEnum.MEDSCI.getType().equals(createReqVO.getSocialType())) {
                JSONObject param = new JSONObject();
                param.put("mobiles", Arrays.asList(createReqVO.getMobile()));
                JSONObject result = HttpRequestUtil.post(openapiHost+"get_batch_query_user_info", true, param);
                // 4.1 如果主站用户不存在，则先插入
                if (result.getJSONObject("data") == null) {
                    HttpRequestUtil.post(openapiHost+"insert_user", true, JSONObject.parseObject(JSONObject.toJSONString(createReqVO)));
                    result = HttpRequestUtil.post(openapiHost+"get_batch_query_user_info", true, param);
                }

                // 4.2 获取主站用户信息，并以主站数据为准
                if (result.getJSONObject("data") != null) {
                    JSONObject msUser = result.getJSONObject("data").getJSONArray("userInfoList").getJSONObject(0);
                    if (msUser != null) {
                        createReqVO.setSocialUserId(msUser.getLong("userId"));
                        createReqVO.setUserName(msUser.getString("userName"));
                        createReqVO.setRealName(msUser.getString("realName"));
                        createReqVO.setOpenid(CommonUtil.high_encryption_id(msUser.getLong("userId")));
                    }
                } else {
                    throw exception(ErrorCodeConstants.ERROR_5038);
                }
            }
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.ERROR_5038);
        }

        // 插入
        MedsciUsers user = BeanUtils.toBean(createReqVO, MedsciUsers.class);
        user.setUserName(getUniqueUserName(user.getUserName(), user.getSocialType()));
        user.setStatus(TRIAL_DAY>0 ? ENABLE : DISABLE);
        user.setExpireAt(LocalDateTime.now().plusDays(TRIAL_DAY));
        String defaultPassword = (StringUtils.isNotBlank(user.getPassword()) && user.getPassword().length() >= BaseConstant.TEN) ?
                user.getPassword() : LocalDateTime.now().format(CommonUtil.DateTimeFormat4) + "@aiBase";
        user.setPassword(passwordEncoder.encode(defaultPassword));
        if (StringUtils.isBlank(user.getAvatar())) {
            user.setAvatar("https://img.medsci.cn/web/img/user_icon.png");
        }

        baseMapper.insert(user);
        updateUserStatus(user);
        // 返回
        return user.getSocialUserId();
    }

    @Override
    public void updateUsers(MedsciUsersSaveReqVO updateReqVO) {
        // 校验存在
        validateUsersExists(updateReqVO.getId());
        // 更新
        MedsciUsers updateObj = BeanUtils.toBean(updateReqVO, MedsciUsers.class);
        MedsciUsers other = getByEmail(updateObj.getEmail(), updateObj.getSocialType());
        if(other!=null && !updateObj.getId().equals(other.getId())){
            throw exception(ErrorCodeConstants.ERROR_5003);
        }

        baseMapper.updateById(updateObj);
        updateUserStatus(updateObj);
        redisManage.updateIsInternalUser(updateObj.getSocialType(), updateObj.getSocialUserId(), updateObj.getIsInternalUser());
    }

    @Override
    public void deleteUsers(Integer id) {
        MedsciUsers user = getUsers(id);
        if (user == null) {
            throw exception(ErrorCodeConstants.ERROR_5001);
        }
        baseMapper.deleteById(user);
    }

    private void validateUsersExists(Integer id) {
        if (baseMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.ERROR_5001);
        }
    }

    @Override
    public MedsciUsers getUsers(Integer id) {
        return baseMapper.selectById(id);
    }

    @Override
    public PageResult<MedsciUsers> selectPage(MedsciUsersPageReqVO pageReqVO) {
        return baseMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MedsciUsers> selectList(MsUserParam param) {
        return baseMapper.selectList(param);
    }

    @Override
    public MedsciUsers getBySocialUserId(Integer socialType, Long socialUserId) {
        MsUserParam userParam = MsUserParam.builder()
                .socialType(socialType)
                .socialUserId(socialUserId)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        MedsciUsers user = list.isEmpty() ? null : list.get(BaseConstant.ZERO);
        if (user != null) {
            redisManage.setIsInternalUser(user.getSocialType(), user.getSocialUserId(), user.getIsInternalUser());
            redisManage.setUserName(user.getSocialType(), user.getSocialUserId(), user.getUserName());
        }

        return user;
    }

    @Override
    public MedsciUsers getBySocialOpenid(Integer socialType, String openid) {
        MsUserParam userParam = MsUserParam.builder()
                .socialType(socialType)
                .openid(openid)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    @Override
    public MedsciUsers getBySocialUserIdAndOpenid(Long socialUserId, String openid) {
        MsUserParam userParam = MsUserParam.builder()
                .socialUserId(socialUserId)
                .openid(openid)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    @Override
    public MedsciUsers getByUserName(String userName, Integer socialType) {
        MsUserParam userParam = MsUserParam.builder()
                .userName(userName)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        if (list.isEmpty()) {
            return null;
        }

        for (MedsciUsers user : list) {
            if (MEDSCI_TYPES.contains(socialType) && MEDSCI_TYPES.contains(user.getSocialType())) {
                return user;
            }
            if (MEDXY_TYPES.contains(socialType) && MEDXY_TYPES.contains(user.getSocialType())) {
                return user;
            }
        }

        return null;
    }

    @Override
    public MedsciUsers getByEmail(String email, String fromPlatform) {
        MsUserParam userParam = MsUserParam.builder()
                .email(email)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        if (list.isEmpty()) {
            return null;
        }

        List<Integer> types = BaseConstant.FROM_XAI.equals(fromPlatform) ? MEDSCI_TYPES : MEDXY_TYPES;

        for (MedsciUsers user : list) {
            if (types.contains(user.getSocialType())) {
                return user;
            }
        }

        return null;
    }

    @Override
    public MedsciUsers getByEmail(String email, Integer socialType) {
        MsUserParam userParam = MsUserParam.builder()
                .email(email)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        if (list.isEmpty()) {
            return null;
        }

        List<Integer> types = MEDSCI_TYPES.contains(socialType) ? MEDSCI_TYPES : (MEDXY_TYPES.contains(socialType) ? MEDXY_TYPES : new ArrayList<>());
        for (MedsciUsers user : list) {
            if (types.contains(user.getSocialType())) {
                return user;
            }
        }

        return null;
    }

    @Override
    public MedsciUsers getByMobile(Integer socialType, String mobile) {
        MsUserParam userParam = MsUserParam.builder()
                .mobile(mobile)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        if (list.isEmpty()) {
            return null;
        }

        List<Integer> types = MEDSCI_TYPES.contains(socialType) ? MEDSCI_TYPES : (MEDXY_TYPES.contains(socialType) ? MEDXY_TYPES : new ArrayList<>());
        for (MedsciUsers user : list) {
            if (types.contains(user.getSocialType())) {
                return user;
            }
        }

        return null;
    }

    /**
     * 更新用户过期状态
     * @param user
     * @return
     */
    @Override
    public Boolean updateUserStatus(MedsciUsers user) {
        if (user.getStatus().equals(ENABLE)) {
            if (user.getExpireAt()==null || LocalDate.now().isAfter(user.getExpireAt().toLocalDate())) {
                user.setStatus(DISABLE);
                baseMapper.updateById(user);
            }
        }

        return true;
    }

    @Override
    public Boolean checkStatusByAuthUser(OAuth2AccessTokenCheckRespDTO authUser) {
        MedsciUsers user = getUserByAuthUser(authUser);
        if (user == null) {
            return false;
        }

        return ENABLE.equals(user.getStatus());
    }

    @Override
    public MedsciUsers getUserByAuthUser(OAuth2AccessTokenCheckRespDTO authUser) {
        if (authUser == null) {
            return null;
        }
        return getBySocialUserId(authUser.getUserType(), authUser.getUserId());
    }

    /**
     * 谷歌和facebook注册及登录
     * @param socialType
     * @param userInfo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MedsciUsers getUser(Integer socialType, String userInfo) {
        MedsciUsers user = null;
        if (AuthDefaultSource.GOOGLE.name().equals(SocialTypeEnum.valueOfType(socialType).getSource())) {
            GoogleUserInfo googleUserInfo = JsonUtils.parseObject(userInfo, GoogleUserInfo.class);
            user = getBySocialUserId(socialType, googleUserInfo.getSocialUserId());
            if (user == null) {
                user = getByEmail(googleUserInfo.getEmail(), socialType);
                if (user != null) {
                    throw exception(ErrorCodeConstants.ERROR_5040);
                }
                user = new MedsciUsers();
                user.setSocialUserId(googleUserInfo.getSocialUserId());
                user.setOpenid(googleUserInfo.getSub());
                user.setSocialType(socialType);
                user.setFirstName(googleUserInfo.getGiven_name());
                user.setLastName(googleUserInfo.getFamily_name());
                user.setEmail(googleUserInfo.getEmail());
                user.setUserName(getUniqueUserName(googleUserInfo.getName(), socialType));
                user.setRealName(googleUserInfo.getName());
                user.setAvatar(googleUserInfo.getPicture());
                user.setMobile(BaseConstant.EMPTY_STR);
                user.setStatus(TRIAL_DAY>0 ? ENABLE : DISABLE);
                user.setExpireAt(LocalDateTime.now().plusDays(TRIAL_DAY));
                user.setPassword(passwordEncoder.encode(LocalDateTime.now().format(CommonUtil.DateTimeFormat4)+"@aiBase"));
                baseMapper.insert(user);
            }
        }
        if (AuthDefaultSource.FACEBOOK.name().equals(SocialTypeEnum.valueOfType(socialType).getSource())) {
            FacebookUserInfo facebookUserInfo = JsonUtils.parseObject(userInfo, FacebookUserInfo.class);
            user = getBySocialUserId(socialType, facebookUserInfo.getSocialUserId());
            if (user == null) {
                user = getByEmail(facebookUserInfo.getEmail(), socialType);
                if (user != null) {
                    throw exception(ErrorCodeConstants.ERROR_5041);
                }
                user = new MedsciUsers();
                user.setSocialUserId(facebookUserInfo.getSocialUserId());
                user.setOpenid(facebookUserInfo.getId());
                user.setSocialType(socialType);
                user.setEmail(facebookUserInfo.getEmail());
                user.setUserName(getUniqueUserName(facebookUserInfo.getName(), socialType));
                user.setRealName(facebookUserInfo.getName());
                user.setAvatar(facebookUserInfo.getPicture().getData().getUrl());
                user.setMobile(BaseConstant.EMPTY_STR);
                user.setStatus(TRIAL_DAY>0 ? ENABLE : DISABLE);
                user.setExpireAt(LocalDateTime.now().plusDays(TRIAL_DAY));
                user.setPassword(passwordEncoder.encode(LocalDateTime.now().format(CommonUtil.DateTimeFormat4)+"@aiBase"));
                baseMapper.insert(user);
            }
        }

        if (user != null) {
            updateUserStatus(user);
        }

        return user;
    }

    /**
     * 为了dify能识别唯一用户
     * @param userName
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String getUniqueUserName(String userName, Integer socialType) {
        if (getByUserName(userName, socialType) == null) {
            return userName;
        }
        String[] strs = userName.split(BaseConstant.UNDER_LINE_STR);
        if (BaseConstant.ONE.equals(strs.length)) {
            return getUniqueUserName(userName+"_001", socialType);
        }
        try {
            String l = strs[strs.length - BaseConstant.ONE];
            Integer num = Integer.parseInt(l) + BaseConstant.ONE;
            userName = userName.replace(l, BaseConstant.EMPTY_STR) + String.format("%03d", num);
            return getUniqueUserName(userName, socialType);
        } catch (Exception e) {
            return getUniqueUserName(userName+"_001", socialType);
        }
    }

    /**
     * 设置国际支付用户ID
     * @param user
     * @return
     */
    @Override
    @Async
    @DS(DBConstant.AiBase)
    public String updateStripeCustomerId(MedsciUsers user) {
        try {
            Customer customer = null;
            if (StringUtils.isNotBlank(user.getStripeCustomerId())) {
                customer = StripeUtil.getCustomer(active, user.getStripeCustomerId());
                if (customer != null) {
                    return customer.getId();
                }
            }

            customer = StripeUtil.getCustomerByEmail(active, user.getEmail());
            if (customer == null) {
                customer = StripeUtil.createCustomer(active, user.getEmail(), user.getRealName());
            }
            if (customer == null) {
                return null;
            }

            user.setStripeCustomerId(customer.getId());
            user.setUpdatedAt(LocalDateTime.now());
            updateById(user);
            return customer.getId();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 支付成功后更新用户 过期信息
     * @param socialUserId
     * @param socialType
     * @param expireAt
     * @return
     */
    @Override
    public Boolean updateExpireAt(Long socialUserId, Integer socialType, LocalDateTime expireAt) {
        MedsciUsers user = getBySocialUserId(socialType, socialUserId);
        if (user != null && expireAt.isAfter(user.getExpireAt())) {
            user.setStatus(ENABLE);
            user.setExpireAt(expireAt);
            user.setUpdatedAt(LocalDateTime.now());
            return updateById(user);
        }
        return true;
    }

    /**
     * 注册
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean register(RegisterReqVO param) {
        if (!yudaoSystemService.checkCode(param.getEmail(), param.getEmailCode())) {
            throw exception(ErrorCodeConstants.ERROR_5053);
        }
        Long socialUserId = oauthService.getSocialUserId(param.getSocialType(), param.getEmail());

        MedsciUsers user = getByEmail(param.getEmail(), param.getSocialType());
        if (user != null) {
            throw exception(ErrorCodeConstants.ERROR_5003);
        }
        String userName = param.getUserName();
        if (StringUtils.isBlank(userName)) {
            String[] strs = param.getEmail().split("@");
            userName = strs[0];
        }

        user = new MedsciUsers();
        user.setSocialUserId(socialUserId); // 自注册
        user.setOpenid(param.getEmail());
        user.setSocialType(param.getSocialType());
        user.setEmail(param.getEmail());
        user.setUserName(getUniqueUserName(userName, param.getSocialType()));
        user.setRealName(userName);
        user.setMobile(BaseConstant.EMPTY_STR);
        user.setPassword(passwordEncoder.encode(param.getPassword()));
        user.setStatus(TRIAL_DAY>0 ? ENABLE : DISABLE);
        user.setExpireAt(LocalDateTime.now().plusDays(TRIAL_DAY));
        Boolean res = baseMapper.insert(user) > BaseConstant.ZERO;
        return res;
    }

    /**
     * 登录
     *
     * @param param
     * @return
     */
    @Override
    public TokenResponse login(LoginReqVO param) {
        MedsciUsers user = getByEmail(param.getEmail(), param.getFromPlatform());
        if (user == null) {
            throw exception(ErrorCodeConstants.ERROR_5001);
        }

        String numStr = redisManage.getErrorNum(param.getEmail());
        Integer num = numStr == null ? BaseConstant.ZERO : Integer.parseInt(numStr);
        if (num >= 5) {
            throw exception(ErrorCodeConstants.ERROR_5042);
        }

        if (!passwordEncoder.matches(param.getPassword(), user.getPassword())) {
            num ++;
            redisManage.updateErrorNum(param.getEmail(), num);
            throw exception(ErrorCodeConstants.ERROR_5043, 5-num);
        }

        // 登录成功删除错误次数
        redisManage.updateErrorNum(param.getEmail(), BaseConstant.ZERO);

        // 更新用户过期状态
        updateUserStatus(user);

        return getToken(user);
    }

    @Override
    public void logout(HttpServletResponse response, HttpServletRequest request, String auth) {
        oauthService.removeAccessToken(auth);
        oauthService.removeCookie(response, request,"yudaoToken");
    }

    @Override
    public TokenResponse socialLogin(Integer socialType, String code, String state) {
        log.info("socialLogin {} ：{} ：{}", socialType, code, state);
        String openid = oauthService.getOauthOpenid(socialType, state, code);
        MedsciUsers user = getBySocialOpenid(socialType, openid);
        if (user == null) {
            throw exception(ErrorCodeConstants.ERROR_5001);
        }
        return getToken(user);
    }

    @Override
    public TokenResponse getAiWriteToken(MedsciUserRequest param, String locale) {
        MedsciUsers user = getUser(param, locale);
        return getToken(user);
    }

    @Override
    public TokenResponse getToken(MedsciUsers user) {
        TokenResponse res = new TokenResponse();
        List<String> roles = Arrays.asList("tenant_admin");
        Integer days = 7;
        if (!"NoLoginUser".equals(user.getOpenid())) {
            user.setPassword(null);
            user.setMobile(null);

            OAuth2AccessTokenCreateReqDTO reqDTO = new OAuth2AccessTokenCreateReqDTO();
            reqDTO.setUserId(user.getSocialUserId());
            reqDTO.setUserType(user.getSocialType()); // WebProperties
            reqDTO.setClientId("ai-base");
            reqDTO.setScopes(roles);
            OAuth2AccessTokenRespDTO respDTO = oauthService.createAccessToken(reqDTO);
            res.setUserId(user.getOpenid());
            res.setOpenid(user.getOpenid());
            res.setSocialUserId(user.getSocialUserId());
            res.setToken(respDTO.getAccessToken());
            res.setSocialType(user.getSocialType());
            res.setUserInfo(user);

            days = 30;
        }

        String hToken = JwtTokenUtil.hasuraTwt(roles, user.getOpenid(), days);
        res.setHToken(hToken);

        updateStripeCustomerId(user);

        return res;
    }

    @Override
    public String getUserName() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        return getUserName(loginUser.getId(), loginUser.getUserType());
    }

    @Override
    public String getUserName(Long socialUserId, Integer socialType) {
        String userName = redisManage.getUserName(socialType, socialUserId);
        if (userName == null) {
            getBySocialUserId(socialType, socialUserId);
        }
        userName = redisManage.getUserName(socialType, socialUserId);
        return userName;
    }

    @Override
    public String getIsInternalUser(Long socialUserId, Integer socialType) {
        String res = redisManage.getIsInternalUser(socialType, socialUserId);
        if (res == null) {
            getBySocialUserId(socialType, socialUserId);
        }
        res = redisManage.getIsInternalUser(socialType, socialUserId);
        return res;
    }

    @Override
    public List<PurchaseRecord> getPurchaseRecords() {
        String data = redisManage.getPurchaseRecords();
        if (data == null) {
            // 生成5-10个随机用户名
            int length = (int) (Math.random() * 6) + 5; // 生成5-10之间的随机数
            List<PurchaseRecord> records = new ArrayList<>();

            // 定义两种会员类型
            String[] membershipTypes = {"Novax Pro", "Elavax Pro"};
            String[] surnames = {
                    "王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
                    "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗",
                    "梁", "宋", "郑", "谢", "韩", "唐", "冯", "于", "董", "萧",
                    "程", "曹", "袁", "邓", "许", "傅", "沈", "曾", "彭", "吕",
                    "苏", "卢", "蒋", "蔡", "贾", "丁", "魏", "薛", "叶", "阎",
                    "余", "潘", "杜", "戴", "夏", "钟", "汪", "田", "任", "姜",
                    "范", "方", "石", "姚", "谭", "廖", "邹", "熊", "金", "陆",
                    "郝", "孔", "白", "崔", "康", "毛", "邱", "秦", "江", "史",
                    "顾", "侯", "邵", "孟", "龙", "万", "段", "漕", "钱", "汤",
                    "尹", "黎", "易", "常", "武", "乔", "贺", "赖", "龚", "文"
                    , "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"
                    , "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"
                    , "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"
                    , "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"
                    , "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"
            };

            for (int i = 0; i < length; i++) {
                // 生成随机用户名
                String randomUserName = surnames[(int) (Math.random() * surnames.length)];
                // 随机选择会员类型
                String selectedMembership = membershipTypes[(int) (Math.random() * 2)];

                // 创建PurchaseRecord对象
                PurchaseRecord record = new PurchaseRecord();
                record.setUserName(randomUserName + "****");
                record.setAppName(selectedMembership);

                records.add(record);
            }

            data = JSON.toJSONString(records);
            redisManage.setPurchaseRecords(data);
        }

        return JSON.parseArray(data, PurchaseRecord.class);
    }

    @Override
    public List<UserImportExcelVO> importUser(List<UserImportExcelVO> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        for (UserImportExcelVO importUser : list) {
            try {
                if (importUser.getSocialUserId() == null || StringUtils.isBlank(importUser.getUserName())
                || StringUtils.isBlank(importUser.getOpenid())) {
                    importUser.setRemark("必填项不能为空");
                    continue;
                }

                importUser.setSocialType(importUser.getSocialType()==null?BaseConstant.ZERO:importUser.getSocialType());
                // 1. 先根据三方平台和主站id来查询是否存在，存在则不处理，不存在则新加
                MedsciUsers existingUser = getBySocialUserId(importUser.getSocialType(), importUser.getSocialUserId());
                if (existingUser != null) {
                    // 用户已存在，跳过处理
                    importUser.setRemark(importUser.getSocialUserId()+" 用户id已存在，不处理");
                    continue;
                }

                // 2. 根据三方平台和邮箱查询是否存在，存在则不处理，不存在则新加
                if (StringUtils.isNotBlank(importUser.getEmail())) {
                    MedsciUsers existingUserByEmail = getByEmail(importUser.getEmail(), importUser.getSocialType());
                    if (existingUserByEmail != null) {
                        // 用户已存在，跳过处理
                        importUser.setRemark(importUser.getEmail()+" 用户邮箱已存在，不处理");
                        continue;
                    }
                }

                // 3. 创建新用户
                MedsciUsers user = BeanUtils.toBean(importUser, MedsciUsers.class);
                user.setUserName(getUniqueUserName(importUser.getUserName(), importUser.getSocialType()));
                user.setStatus(TRIAL_DAY>0 ? ENABLE : DISABLE);
                user.setExpireAt(LocalDateTime.now().plusDays(TRIAL_DAY));
                String defaultPassword = (StringUtils.isNotBlank(importUser.getPassword()) && importUser.getPassword().length() >= BaseConstant.TEN) ?
                        importUser.getPassword() : LocalDateTime.now().format(CommonUtil.DateTimeFormat4) + "@aiBase";
                user.setPassword(passwordEncoder.encode(defaultPassword));
                if (StringUtils.isBlank(importUser.getAvatar())) {
                    user.setAvatar("https://img.medsci.cn/web/img/user_icon.png");
                }

                // 保存用户
                baseMapper.insert(user);

                // 添加到成功列表
                importUser.setRemark("成功");

            } catch (Exception e) {
                importUser.setRemark("报错："+e.getMessage());
                log.error("导入用户失败: {}", importUser, e);
                // 可以选择继续处理下一个用户，或者抛出异常
            }
        }

        return list;
    }

}
