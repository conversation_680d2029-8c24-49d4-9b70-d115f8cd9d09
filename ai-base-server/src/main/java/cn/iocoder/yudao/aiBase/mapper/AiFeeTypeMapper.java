package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.dto.param.AiFeeTypeParam;
import cn.iocoder.yudao.aiBase.dto.request.AiFeeTypePageReqVO;
import cn.iocoder.yudao.aiBase.entity.AiFeeType;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * AI费用类型 Mapper
 * 
 * <AUTHOR>
 * @since 2023-09-15
 */
@Mapper
public interface AiFeeTypeMapper extends BaseMapperX<AiFeeType> {

    default List<AiFeeType> selectList(AiFeeTypeParam reqVO) {
        return selectList(new LambdaQueryWrapperX<AiFeeType>()
            .eqIfPresent(AiFeeType::getAppUuid, reqVO.getAppUuid())
            .eqIfPresent(AiFeeType::getPackageKey, reqVO.getPackageKey())
            .eqIfPresent(AiFeeType::getPackageType, reqVO.getPackageType())
            .eqIfPresent(AiFeeType::getPriceId, reqVO.getPriceId())
            .eqIfPresent(AiFeeType::getOnline, reqVO.getOnline())
            .eqIfPresent(AiFeeType::getPriceType, reqVO.getPriceType())
            .likeIfPresent(AiFeeType::getRemark, reqVO.getRemark())
            .orderByAsc(AiFeeType::getOrderNum)
            .orderByDesc(AiFeeType::getId));
    }

    default PageResult<AiFeeType> selectPage(AiFeeTypeParam reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiFeeType>()
            .eqIfPresent(AiFeeType::getAppUuid, reqVO.getAppUuid())
            .eqIfPresent(AiFeeType::getPackageKey, reqVO.getPackageKey())
            .eqIfPresent(AiFeeType::getPackageType, reqVO.getPackageType())
            .eqIfPresent(AiFeeType::getPriceId, reqVO.getPriceId())
            .eqIfPresent(AiFeeType::getOnline, reqVO.getOnline())
            .eqIfPresent(AiFeeType::getPriceType, reqVO.getPriceType())
            .likeIfPresent(AiFeeType::getRemark, reqVO.getRemark())
            .orderByDesc(AiFeeType::getId)
            .orderByAsc(AiFeeType::getOrderNum));
    }

    default PageResult<AiFeeType> selectFeeTypePage(AiFeeTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiFeeType>()
            .eqIfPresent(AiFeeType::getAppUuid, reqVO.getAppUuid())
            .eqIfPresent(AiFeeType::getLang, reqVO.getLang())
            .eqIfPresent(AiFeeType::getPackageKey, reqVO.getPackageKey())
            .eqIfPresent(AiFeeType::getPackageType, reqVO.getPackageType())
            .eqIfPresent(AiFeeType::getPriceId, reqVO.getPriceId())
            .eqIfPresent(AiFeeType::getOnline, reqVO.getOnline())
            .eqIfPresent(AiFeeType::getPriceType, reqVO.getPriceType())
            .likeIfPresent(AiFeeType::getRemark, reqVO.getRemark())
            .betweenIfPresent(AiFeeType::getCreatedAt, reqVO.getCreateTime())
            .orderByDesc(AiFeeType::getId)
            .orderByAsc(AiFeeType::getOrderNum));
    }

}
