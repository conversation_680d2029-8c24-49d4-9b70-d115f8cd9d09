package cn.iocoder.yudao.aiBase.dto.request;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - AI费用类型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AiFeeTypePageReqVO extends PageParam {

    @Schema(description = "应用uuid", example = "app-uuid-123")
    private String appUuid;

    @Schema(description = "应用语言", example = "zh")
    private String lang;

    @Schema(description = "订阅套餐，一般是appNameEn", example = "ai-writer")
    private String packageKey;

    @Schema(description = "订阅方式", example = "连续包月")
    private String packageType;

    @Schema(description = "价格ID，币种是人民币的是支付宝场景值，美元的是stripe", example = "price_123")
    private String priceId;

    @Schema(description = "1开启，0下架", example = "1")
    private Integer online;

    @Schema(description = "支付宝订阅，stripe订阅，按次收费", example = "支付宝订阅")
    private String priceType;

    @Schema(description = "备注", example = "测试")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime[] createTime;

}
