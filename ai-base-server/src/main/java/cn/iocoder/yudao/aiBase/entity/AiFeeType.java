package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("ai_fee_type")
@KeySequence("ai_fee_type_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiFeeType extends Model<AiFeeType> {

    @Schema(description = "主键")
    @TableId
    private Integer id;

    @Schema(description = "应用uuid")
    private String appUuid;

    @Schema(description = "应用语言")
    private String lang;

    @Schema(description = "订阅套餐，一般是appNameEn")
    private String packageKey;

    @Schema(description = "订阅方式")
    private String packageType;

    @Schema(description = "周期类型，默认月")
    private String periodType = "MONTH";

    @Schema(description = "时长")
    private Integer monthNum = 1;

    @Schema(description = "原价格")
    private BigDecimal oldPrice;

    @Schema(description = "价格")
    private BigDecimal feePrice;

    @Schema(description = "币种")
    private String coinType = "人民币";

    @Schema(description = "价格ID，币种是人民币的是支付宝场景值，美元的是stripe")
    private String priceId;

    @Schema(description = "可使用次数，-1不限制")
    private Integer num;

    @Schema(description = "1开启，0下架")
    private Integer online;

    @Schema(description = "支付宝订阅，stripe订阅，按次收费")
    private String priceType;

    @Schema(description = "购买后的有效期")
    private Integer expiredMonths;

    @Schema(description = "排序，越小越前")
    private Integer orderNum;

    @Schema(description = "备注")
    private String remark;

    @Schema(description =  "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;

    @Schema(description =  "更新时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;

    @Schema(description =  "0正常 1删除")
    @TableLogic
    private Integer deleted;
}
